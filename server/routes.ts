import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { setupAuth, isAuthenticated } from "./replitAuth";
import {
  insertEsoSchema,
  insertStartupIdeaSchema,
  insertTeamSchema,
  insertMilestoneProgressSchema,
  insertMentorProfileSchema,
  insertSponsorProfileSchema,
} from "@shared/schema";
import multer from "multer";
import { Client } from "@replit/object-storage";

// multer config
const multerStorage = multer.memoryStorage();
const multerUpload = multer({
  storage: multerStorage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10 MB file size limit});
});

// multer config for larger files (strategic documents)
const multerUploadLarge = multer({
  storage: multerStorage,
  limits: { fileSize: 50 * 1024 * 1024 }, // 50 MB file size limit
});

export async function registerRoutes(app: Express): Promise<Server> {
  // Auth middleware
  await setupAuth(app);

  // Auth routes
  app.get("/api/auth/user", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const user = await storage.getUser(userId);
      res.json(user);
    } catch (error) {
      console.error("Error fetching user:", error);
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });

  // File upload endpoint
  app.post(
    "/api/upload",
    isAuthenticated,
    multerUploadLarge.single("file"),
    async (req: any, res) => {
      try {
        const file = req.file as Express.Multer.File;
        if (!file) {
          return res.status(400).json({ message: "No file uploaded" });
        }

        const objectStorageClient = new Client();
        const key = `documents/${Date.now()}-${file.originalname}`;
        const uploadedFileUrl = await objectStorageClient.uploadFromBytes(key, file.buffer);

        res.json({ url: uploadedFileUrl });
      } catch (error) {
        console.error("Error uploading file:", error);
        res.status(500).json({ message: "Failed to upload file" });
      }
    }
  );

  // User profile routes
  app.put("/api/users/profile", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const { role, location, interests, teamPreference } = req.body;

      const updatedUser = await storage.upsertUser({
        id: userId,
        email: req.user.claims.email,
        firstName: req.user.claims.first_name,
        lastName: req.user.claims.last_name,
        profileImageUrl: req.user.claims.profile_image_url,
        role,
        location,
        interests,
        teamPreference,
      });

      res.json(updatedUser);
    } catch (error) {
      console.error("Error updating profile:", error);
      res.status(500).json({ message: "Failed to update profile" });
    }
  });

  // ESO routes
  app.post("/api/esos", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const validatedData = insertEsoSchema.parse({
        ...req.body,
        adminUserId: userId,
      });

      const eso = await storage.createEso(validatedData);
      res.json(eso);
    } catch (error) {
      console.error("Error creating ESO:", error);
      res.status(500).json({ message: "Failed to create ESO" });
    }
  });

  app.get("/api/esos/my", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const eso = await storage.getEsoByAdminId(userId);
      res.json(eso);
    } catch (error) {
      console.error("Error fetching ESO:", error);
      res.status(500).json({ message: "Failed to fetch ESO" });
    }
  });

  // Startup ideas routes
  app.get("/api/startup-ideas", isAuthenticated, async (req: any, res) => {
    try {
      const { esoId } = req.query;
      let ideas = await storage.getStartupIdeas(
        esoId ? parseInt(esoId) : undefined,
      );

      // Always ensure curated ideas exist alongside user-submitted ones
      await seedCuratedIdeas();
      ideas = await storage.getStartupIdeas(
        esoId ? parseInt(esoId) : undefined,
      );

      res.json(ideas);
    } catch (error) {
      console.error("Error fetching startup ideas:", error);
      res.status(500).json({ message: "Failed to fetch startup ideas" });
    }
  });

  // Helper function to seed curated ideas
  async function seedCuratedIdeas() {
    // Check if curated ideas already exist to avoid duplicates
    const existingIdeas = await storage.getStartupIdeas();
    const curatedTitles = [
      "EcoDelivery",
      "SkillSwap",
      "UrbanHive",
      "WellnessAI",
      "CraftChain",
      "GreenCommute",
    ];
    const existingTitles = existingIdeas.map((idea) => idea.title);

    const curatedIdeas = [
      {
        title: "EcoDelivery",
        description:
          "AI-powered sustainable food delivery platform connecting local farms with urban consumers through optimized routes and carbon-neutral delivery.",
        category: "Food Tech",
        isPremium: false,
      },
      {
        title: "SkillSwap",
        description:
          "Peer-to-peer learning platform where professionals teach each other skills in exchange for credits, creating a skill-based economy.",
        category: "EdTech",
        isPremium: false,
      },
      {
        title: "UrbanHive",
        description:
          "Smart city solution that optimizes urban resource allocation using IoT sensors and predictive analytics for waste, energy, and water management.",
        category: "Smart City",
        isPremium: true,
      },
      {
        title: "WellnessAI",
        description:
          "Personal health companion that provides customized wellness plans using wearable data, mental health tracking, and lifestyle optimization.",
        category: "HealthTech",
        isPremium: false,
      },
      {
        title: "CraftChain",
        description:
          "Blockchain marketplace for artisans and craftspeople to sell authentic handmade products with verified provenance and fair trade practices.",
        category: "Marketplace",
        isPremium: true,
      },
      {
        title: "GreenCommute",
        description:
          "Carbon footprint tracking app that gamifies sustainable transportation choices and rewards users for eco-friendly commuting decisions.",
        category: "Climate Tech",
        isPremium: false,
      },
    ];

    for (const idea of curatedIdeas) {
      if (!existingTitles.includes(idea.title)) {
        try {
          console.log(`Creating curated idea: ${idea.title}`);
          await storage.createStartupIdea(idea);
        } catch (error) {
          console.log(`Error creating idea ${idea.title}:`, error);
        }
      }
    }
  }

  app.post("/api/startup-ideas", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const validatedData = insertStartupIdeaSchema.parse({
        ...req.body,
        createdById: userId,
      });

      const idea = await storage.createStartupIdea(validatedData);
      res.json(idea);
    } catch (error) {
      console.error("Error creating startup idea:", error);
      res.status(500).json({ message: "Failed to create startup idea" });
    }
  });

  app.get("/api/startup-ideas/:id", isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const idea = await storage.getStartupIdeaById(id);

      if (!idea) {
        return res.status(404).json({ message: "Startup idea not found" });
      }

      res.json(idea);
    } catch (error) {
      console.error("Error fetching startup idea:", error);
      res.status(500).json({ message: "Failed to fetch startup idea" });
    }
  });

  // Teams routes
  app.post("/api/teams", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const validatedData = insertTeamSchema.parse({
        ...req.body,
        leaderId: userId,
      });

      const team = await storage.createTeam(validatedData);
      res.json(team);
    } catch (error) {
      console.error("Error creating team:", error);
      res.status(500).json({ message: "Failed to create team" });
    }
  });

  app.get("/api/teams/my", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const teams = await storage.getTeamsByUserId(userId);
      res.json(teams);
    } catch (error) {
      console.error("Error fetching teams:", error);
      res.status(500).json({ message: "Failed to fetch teams" });
    }
  });

  app.get("/api/teams/:id/members", isAuthenticated, async (req, res) => {
    try {
      const teamId = parseInt(req.params.id);
      const members = await storage.getTeamMembers(teamId);
      res.json(members);
    } catch (error) {
      console.error("Error fetching team members:", error);
      res.status(500).json({ message: "Failed to fetch team members" });
    }
  });

  // Milestones routes
  app.get(
    "/api/milestones/startup-idea/:id",
    isAuthenticated,
    async (req, res) => {
      try {
        const startupIdeaId = parseInt(req.params.id);
        const milestones = await storage.getMilestonesByIdeaId(startupIdeaId);
        res.json(milestones);
      } catch (error) {
        console.error("Error fetching milestones:", error);
        res.status(500).json({ message: "Failed to fetch milestones" });
      }
    },
  );

  app.get(
    "/api/milestone-progress/my",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.claims.sub;
        const progress = await storage.getMilestoneProgressByUserId(userId);
        res.json(progress);
      } catch (error) {
        console.error("Error fetching milestone progress:", error);
        res.status(500).json({ message: "Failed to fetch milestone progress" });
      }
    },
  );

  app.post(
    "/api/milestone-progress",
    isAuthenticated,
    multerUpload.array("files", 10),
    async (req: any, res) => {
      try {
        const userId = req.user.claims.sub;
        // replit object storage client
        const objectStorageClient = new Client();

        const files = req.files as Express.Multer.File[];
        console.log("Received files:", files);
        console.log(req.body);
        const fileUploadPromises = files.map((file) => {
          // Create a unique name for the file in the bucket
          const key = `mp-attachments/${Date.now()}-${file.originalname}`;

          // The upload method takes the key (path/filename) and the content (buffer)
          return objectStorageClient.uploadFromBytes(key, file.buffer);
        });
        const uploadedFileUrls = await Promise.all(fileUploadPromises);
        console.log("Uploaded File URLs:", uploadedFileUrls);
        const validatedData = insertMilestoneProgressSchema.parse({
          ...req.body,
          milestoneId: parseInt(req.body.milestoneId),
          userId,
        });

        const progress = await storage.updateMilestoneProgress(validatedData);
        res.json(progress);
      } catch (error) {
        console.error("Error updating milestone progress:", error);
        res
          .status(500)
          .json({ message: "Failed to update milestone progress" });
      }
    },
  );

  // Mentor routes
  app.post("/api/mentor-profile", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const validatedData = insertMentorProfileSchema.parse({
        ...req.body,
        userId,
      });

      const profile = await storage.createMentorProfile(validatedData);
      res.json(profile);
    } catch (error) {
      console.error("Error creating mentor profile:", error);
      res.status(500).json({ message: "Failed to create mentor profile" });
    }
  });

  app.get("/api/mentors", isAuthenticated, async (req, res) => {
    try {
      const { expertise } = req.query;
      const expertiseArray = expertise ? (expertise as string).split(",") : [];
      const mentors = await storage.getMentorsByExpertise(expertiseArray);
      res.json(mentors);
    } catch (error) {
      console.error("Error fetching mentors:", error);
      res.status(500).json({ message: "Failed to fetch mentors" });
    }
  });

  // ESO routes
  app.get("/api/eso-profile", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const esoProfile = await storage.getEsoByAdminUserId(userId);

      // Return null if no profile exists, don't return undefined
      res.json(esoProfile || null);
    } catch (error) {
      console.error("Error fetching ESO profile:", error);
      res.status(500).json({ message: "Failed to fetch ESO profile" });
    }
  });

  app.post("/api/eso-profile", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const validatedData = insertEsoSchema.parse({
        ...req.body,
        adminUserId: userId,
        profileCompleted: true,
      });

      const profile = await storage.createEso(validatedData);

      // Mark user profile as completed
      await storage.upsertUser({
        id: userId,
        email: req.user.claims.email,
        firstName: req.user.claims.first_name,
        lastName: req.user.claims.last_name,
        profileImageUrl: req.user.claims.profile_image_url,
        profileCompleted: true,
      });

      res.json(profile);
    } catch (error) {
      console.error("Error creating ESO profile:", error);
      res.status(500).json({ message: "Failed to create ESO profile" });
    }
  });

  // Sponsor routes
  app.post("/api/sponsor-profile", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const validatedData = insertSponsorProfileSchema.parse({
        ...req.body,
        userId,
      });

      const profile = await storage.createSponsorProfile(validatedData);
      res.json(profile);
    } catch (error) {
      console.error("Error creating sponsor profile:", error);
      res.status(500).json({ message: "Failed to create sponsor profile" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
