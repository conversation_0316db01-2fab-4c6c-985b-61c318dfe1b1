import {
  users,
  esos,
  startupIdeas,
  teams,
  teamMembers,
  milestones,
  userMilestoneProgress,
  mentorProfiles,
  sponsorProfiles,
  mentorAssignments,
  type User,
  type UpsertUser,
  type InsertEso,
  type Eso,
  type InsertStartupIdea,
  type StartupIdea,
  type InsertTeam,
  type Team,
  type TeamMember,
  type Milestone,
  type UserMilestoneProgress,
  type InsertMilestoneProgress,
  type MentorProfile,
  type SponsorProfile,
} from "@shared/schema";
import { db } from "./db";
import { eq, and, desc, asc } from "drizzle-orm";

export interface IStorage {
  // User operations (mandatory for Replit Auth)
  getUser(id: string): Promise<User | undefined>;
  upsertUser(user: UpsertUser): Promise<User>;
  
  // ESO operations
  createEso(eso: InsertEso): Promise<Eso>;
  getEsoByAdminUserId(adminUserId: string): Promise<Eso | undefined>;
  
  // Startup ideas
  getStartupIdeas(esoId?: number): Promise<StartupIdea[]>;
  createStartupIdea(idea: InsertStartupIdea): Promise<StartupIdea>;
  getStartupIdeaById(id: number): Promise<StartupIdea | undefined>;
  
  // Teams
  createTeam(team: InsertTeam): Promise<Team>;
  getTeamsByUserId(userId: string): Promise<Team[]>;
  getTeamById(id: number): Promise<Team | undefined>;
  getTeamMembers(teamId: number): Promise<TeamMember[]>;
  addTeamMember(teamId: number, userId: string, role: string): Promise<TeamMember>;
  
  // Milestones
  getMilestonesByIdeaId(startupIdeaId: number): Promise<Milestone[]>;
  createDefaultMilestones(startupIdeaId: number): Promise<Milestone[]>;
  getMilestoneProgressByUserId(userId: string): Promise<UserMilestoneProgress[]>;
  updateMilestoneProgress(progress: InsertMilestoneProgress): Promise<UserMilestoneProgress>;
  
  // Mentor operations
  createMentorProfile(profile: Omit<MentorProfile, 'id' | 'createdAt'>): Promise<MentorProfile>;
  getMentorsByExpertise(expertise: string[]): Promise<(MentorProfile & { user: User })[]>;
  
  // Sponsor operations
  createSponsorProfile(profile: Omit<SponsorProfile, 'id' | 'createdAt'>): Promise<SponsorProfile>;
}

export class DatabaseStorage implements IStorage {
  async getUser(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(userData)
      .onConflictDoUpdate({
        target: users.id,
        set: {
          ...userData,
          updatedAt: new Date(),
        },
      })
      .returning();
    return user;
  }

  async createEso(esoData: InsertEso): Promise<Eso> {
    const [eso] = await db
      .insert(esos)
      .values(esoData)
      .returning();
    return eso;
  }

  async getEsoByAdminUserId(adminUserId: string): Promise<Eso | undefined> {
    const [eso] = await db
      .select()
      .from(esos)
      .where(eq(esos.adminUserId, adminUserId));
    return eso;
  }

  async getStartupIdeas(esoId?: number): Promise<StartupIdea[]> {
    const query = db.select().from(startupIdeas).orderBy(desc(startupIdeas.createdAt));
    
    if (esoId) {
      return await query.where(eq(startupIdeas.esoId, esoId));
    }
    
    return await query;
  }

  async createStartupIdea(ideaData: InsertStartupIdea): Promise<StartupIdea> {
    const [idea] = await db
      .insert(startupIdeas)
      .values(ideaData)
      .returning();
    
    // Create default milestones for the idea
    await this.createDefaultMilestones(idea.id);
    
    return idea;
  }

  async getStartupIdeaById(id: number): Promise<StartupIdea | undefined> {
    const [idea] = await db
      .select()
      .from(startupIdeas)
      .where(eq(startupIdeas.id, id));
    return idea;
  }

  async createTeam(teamData: InsertTeam): Promise<Team> {
    const [team] = await db
      .insert(teams)
      .values(teamData)
      .returning();
    
    // Add team leader as first member
    await this.addTeamMember(team.id, team.leaderId, "Team Lead");
    
    return team;
  }

  async getTeamsByUserId(userId: string): Promise<Team[]> {
    const userTeams = await db
      .select({
        id: teams.id,
        name: teams.name,
        startupIdeaId: teams.startupIdeaId,
        leaderId: teams.leaderId,
        description: teams.description,
        createdAt: teams.createdAt,
      })
      .from(teams)
      .innerJoin(teamMembers, eq(teamMembers.teamId, teams.id))
      .where(eq(teamMembers.userId, userId));
    
    return userTeams;
  }

  async getTeamById(id: number): Promise<Team | undefined> {
    const [team] = await db
      .select()
      .from(teams)
      .where(eq(teams.id, id));
    return team;
  }

  async getTeamMembers(teamId: number): Promise<TeamMember[]> {
    return await db
      .select()
      .from(teamMembers)
      .where(eq(teamMembers.teamId, teamId))
      .orderBy(teamMembers.joinedAt);
  }

  async addTeamMember(teamId: number, userId: string, role: string): Promise<TeamMember> {
    const [member] = await db
      .insert(teamMembers)
      .values({
        teamId,
        userId,
        role,
        status: "active",
      })
      .returning();
    return member;
  }

  async getMilestonesByIdeaId(startupIdeaId: number): Promise<Milestone[]> {
    return await db
      .select()
      .from(milestones)
      .where(eq(milestones.startupIdeaId, startupIdeaId))
      .orderBy(asc(milestones.order));
  }

  async createDefaultMilestones(startupIdeaId: number): Promise<Milestone[]> {
    const defaultMilestones = [
      {
        startupIdeaId,
        title: "Market Research",
        description: "Analyze target market, competition, and customer needs",
        order: 1,
        requirements: ["Market analysis document", "Competitor research", "Customer interviews"],
        estimatedDays: 14,
      },
      {
        startupIdeaId,
        title: "Business Model Canvas",
        description: "Create comprehensive business model canvas",
        order: 2,
        requirements: ["Complete business model canvas", "Value proposition validation", "Revenue model"],
        estimatedDays: 10,
      },
      {
        startupIdeaId,
        title: "MVP Development",
        description: "Build minimum viable product",
        order: 3,
        requirements: ["Working prototype", "Core features implementation", "Technical documentation"],
        estimatedDays: 30,
      },
      {
        startupIdeaId,
        title: "User Testing",
        description: "Conduct user testing and gather feedback",
        order: 4,
        requirements: ["User testing plan", "Feedback collection", "Iteration recommendations"],
        estimatedDays: 14,
      },
    ];

    const createdMilestones = await db
      .insert(milestones)
      .values(defaultMilestones)
      .returning();

    return createdMilestones;
  }

  async getMilestoneProgressByUserId(userId: string): Promise<UserMilestoneProgress[]> {
    return await db
      .select()
      .from(userMilestoneProgress)
      .where(eq(userMilestoneProgress.userId, userId))
      .orderBy(userMilestoneProgress.updatedAt);
  }

  async updateMilestoneProgress(progressData: InsertMilestoneProgress): Promise<UserMilestoneProgress> {
    const [progress] = await db
      .insert(userMilestoneProgress)
      .values({
        ...progressData,
        updatedAt: new Date(),
      })
      .onConflictDoUpdate({
        target: [userMilestoneProgress.userId, userMilestoneProgress.milestoneId],
        set: {
          ...progressData,
          updatedAt: new Date(),
        },
      })
      .returning();

    return progress;
  }

  async createMentorProfile(profileData: Omit<MentorProfile, 'id' | 'createdAt'>): Promise<MentorProfile> {
    const [profile] = await db
      .insert(mentorProfiles)
      .values(profileData)
      .returning();
    return profile;
  }

  async getMentorsByExpertise(expertise: string[]): Promise<(MentorProfile & { user: User })[]> {
    // This would need a more complex query in a real implementation
    // For now, returning all mentors
    const mentors = await db
      .select()
      .from(mentorProfiles)
      .innerJoin(users, eq(mentorProfiles.userId, users.id));

    return mentors.map(mentor => ({
      ...mentor.mentor_profiles,
      user: mentor.users,
    }));
  }

  async createSponsorProfile(profileData: Omit<SponsorProfile, 'id' | 'createdAt'>): Promise<SponsorProfile> {
    const [profile] = await db
      .insert(sponsorProfiles)
      .values(profileData)
      .returning();
    return profile;
  }
}

export const storage = new DatabaseStorage();
