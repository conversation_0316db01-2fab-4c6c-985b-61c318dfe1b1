# Traction - Milestone-Based Startup Development Platform

## Overview

Traction is a responsive web application by Skills Gap Advocate Inc. designed to support aspiring entrepreneurs through structured milestone-based startup development. The platform facilitates connections between entrepreneurs, ESO (Entrepreneur Support Organizations) admins, mentors, and sponsors to foster innovation and business growth.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with shadcn/ui component library
- **Routing**: Wouter (lightweight routing library)
- **State Management**: TanStack Query (React Query) for server state
- **Build Tool**: Vite for development and production builds
- **Form Handling**: React Hook Form with Zod validation

### Backend Architecture
- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js REST API
- **Database ORM**: Drizzle ORM with PostgreSQL
- **Session Management**: Express sessions with PostgreSQL store
- **Build Tool**: ESBuild for production bundling

### Database Design
- **Primary Database**: PostgreSQL (Neon serverless)
- **Schema Management**: Drizzle migrations
- **Key Entities**:
  - Users (entrepreneurs, ESO admins, mentors, sponsors)
  - ESOs (Entrepreneur Support Organizations)
  - Startup ideas and teams
  - Milestone tracking and progress
  - Mentor/sponsor profiles and assignments

## Key Components

### Authentication System
- **Provider**: Replit Auth (OpenID Connect)
- **Session Storage**: PostgreSQL with connect-pg-simple
- **User Roles**: Entrepreneur, ESO Admin, Mentor, Sponsor
- **Security**: HTTPS-only cookies, secure session management

### User Management
- Multi-role system with role-based dashboard routing
- Profile management with interests, location, team preferences
- User onboarding flow with role selection

### Milestone System
- Structured milestone progression (locked → in_progress → completed → under_review)
- File upload capabilities for milestone evidence
- Mentor review and feedback workflow
- Progress tracking and analytics

### Team Management
- Team formation and member management
- Role-based permissions within teams
- Team activity tracking and collaboration tools

## Data Flow

1. **User Authentication**: Replit Auth → Session Creation → Role-based Routing
2. **Milestone Progression**: Upload Evidence → Mentor Review → Status Update → Next Milestone Unlock
3. **Team Collaboration**: Team Formation → Member Invitation → Shared Milestone Progress
4. **ESO Management**: Admin Dashboard → User/Team Oversight → Progress Analytics

## External Dependencies

### Database & Infrastructure
- **Neon Database**: Serverless PostgreSQL hosting
- **Replit**: Development environment and authentication provider

### Frontend Libraries
- **shadcn/ui**: Comprehensive React component library
- **Radix UI**: Headless UI primitives for accessibility
- **Lucide React**: Icon library
- **TanStack Query**: Data fetching and caching
- **Tailwind CSS**: Utility-first CSS framework

### Backend Libraries
- **Express**: Web application framework
- **Drizzle ORM**: Type-safe database operations
- **Passport**: Authentication middleware
- **Express Session**: Session management

## Deployment Strategy

### Development Environment
- **Local Development**: `npm run dev` runs both client and server concurrently
- **Hot Reload**: Vite HMR for frontend, tsx for backend development
- **Database**: Neon serverless PostgreSQL

### Production Build
- **Frontend**: Vite builds to `dist/public`
- **Backend**: ESBuild bundles server to `dist/index.js`
- **Static Files**: Served from Express in production
- **Deployment Target**: Replit autoscale deployment

### Database Management
- **Migrations**: Drizzle Kit manages schema migrations
- **Schema**: Centralized in `shared/schema.ts` for type safety
- **Environment**: DATABASE_URL required for all environments

### Key Configuration Files
- **Vite Config**: Handles React build, path aliases, and development plugins
- **Drizzle Config**: Database connection and migration settings
- **TypeScript**: Monorepo structure with shared types
- **Tailwind**: Custom design system with CSS variables