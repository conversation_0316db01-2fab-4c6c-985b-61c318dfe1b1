modules = ["nodejs-20", "web", "postgresql-16"]
run = "npm run dev"
hidden = [".config", ".git", "generated-icon.png", "node_modules", "dist"]

[nix]
channel = "stable-24_05"

[deployment]
deploymentTarget = "autoscale"
build = ["npm", "run", "build"]
run = ["npm", "run", "start"]

[[ports]]
localPort = 5000
externalPort = 80

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Start application"

[[workflows.workflow]]
name = "Start application"
author = "agent"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run dev"
waitForPort = 5000

[[workflows.workflow]]
name = "Update DB"
mode = "sequential"
author = 36400264

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run db:push"

[[workflows.workflow]]
name = "npm install"
mode = "parallel"
author = 36400264

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm install"

[objectStorage]
defaultBucketID = "replit-objstore-66b65e52-a5bd-4796-9724-2370ea7d88cb"
