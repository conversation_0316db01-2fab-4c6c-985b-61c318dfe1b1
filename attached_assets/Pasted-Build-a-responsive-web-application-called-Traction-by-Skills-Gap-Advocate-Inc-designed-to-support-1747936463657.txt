Build a responsive web application called Traction (by Skills Gap Advocate Inc.) designed to support aspiring entrepreneurs through structured milestone-based startup development. The application should have four main user roles: Entrepreneurs, ESOs (Entrepreneur Support Organizations) as Admins, Mentors, and Sponsors.

Core Functionality by Role

Entrepreneur User Flow

Sign up and complete onboarding (select interests, location, team/solo).

Browse AI-curated startup ideas or submit their own.

Join or form a team.

View a milestone-based roadmap specific to each idea.

Upload milestone progress (docs, videos, images).

Receive expert feedback and unlock next milestones.

Track team activity and view sponsor contributions.

Optional: pay to unlock premium ideas or mentorship.

Admin (ESO) Flow

ESO signs up via a dedicated Admin Onboarding Portal including:

Organization name and location.

Description of goals, community needs, and aspirational cities (e.g., “We want to become like Austin or Medellín”).

Upload strategic documents, reports, or core community issues to seed AI idea generation.

Admin dashboard features:

View and manage all entrepreneurs and teams under their license.

Upload or curate new startup ideas for local relevance.

View AI-generated suggestions based on uploaded issues and aspirational cities.

Configure payment settings:

Choose between direct license payment, sponsorship-led revenue, or revenue share per milestone.

Option to retain a percentage of all sponsorship revenue (set %).

Set pricing for idea unlocks or mentorship access.

Track progress of cohorts via analytics dashboard.

Set visibility permissions for sponsors and external mentors.

Assign reviewers, mentors, or sponsors to specific milestones.

White-label configuration: customize brand name, color scheme, and messaging for ESO deployment.

Mentor User Flow

Create a mentor profile with expertise, industries, and availability.

Receive suggested milestone submissions that align with their background.

Provide written or video feedback to entrepreneurs.

Accept or decline review invitations from ESOs.

Track their mentee outcomes and engagement history.

Sponsor User Flow

Create a sponsor account with funding categories or interests.

Browse startup ideas or milestone progress within ESO regions.

Select and fund milestones directly (individually or by package).

View reporting dashboards showing funded project outcomes.

Set recurring sponsorship terms or direct impact goals.