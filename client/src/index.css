@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 210 11% 98%;
  --foreground: 20 14.3% 4.1%;
  --muted: 210 9% 96%;
  --muted-foreground: 25 5.3% 44.7%;
  --popover: 210 11% 98%;
  --popover-foreground: 20 14.3% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 20 14.3% 4.1%;
  --border: 214 13% 90%;
  --input: 214 13% 90%;
  --primary: 207 90% 54%;
  --primary-foreground: 211 100% 99%;
  --secondary: 158 64% 52%;
  --secondary-foreground: 0 0% 98%;
  --accent: 43 96% 56%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --ring: 207 90% 54%;
  --radius: 0.5rem;
  
  /* Additional custom properties for the design */
  --neutral-50: 210 11% 98%;
  --neutral-100: 214 13% 96%;
  --neutral-200: 213 13% 88%;
  --neutral-300: 212 13% 81%;
  --neutral-400: 213 9% 58%;
  --neutral-500: 215 8% 45%;
  --neutral-600: 215 14% 34%;
  --neutral-700: 216 19% 25%;
  --neutral-800: 217 25% 18%;
  --neutral-900: 222 25% 11%;
}

.dark {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --primary: 207 90% 54%;
  --primary-foreground: 211 100% 99%;
  --secondary: 158 64% 52%;
  --secondary-foreground: 0 0% 98%;
  --accent: 43 96% 56%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 240 4.9% 83.9%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  .bg-neutral-50 {
    background-color: hsl(var(--neutral-50));
  }
  
  .text-neutral-600 {
    color: hsl(var(--neutral-600));
  }
  
  .text-neutral-700 {
    color: hsl(var(--neutral-700));
  }
  
  .text-neutral-800 {
    color: hsl(var(--neutral-800));
  }
  
  .text-neutral-900 {
    color: hsl(var(--neutral-900));
  }
  
  .border-neutral-200 {
    border-color: hsl(var(--neutral-200));
  }
}

@layer components {
  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--neutral-400)) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: hsl(var(--neutral-400));
    border-radius: 3px;
  }
}
