import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import ProtectedRoute from "@/components/ProtectedRoute";
import Dashboard from "@/pages/dashboard";
import Ideas from "@/pages/ideas";
import Teams from "@/pages/teams";
import Mentors from "@/pages/mentors";
import ProjectDetails from "@/pages/project-details";
import Login from "@/pages/login";
import Onboarding from "@/pages/onboarding";
import NotFound from "@/pages/not-found";

function Router() {
  return (
    <ProtectedRoute>
      <Switch>
        <Route path="/" component={Dashboard} />
        <Route path="/ideas" component={Ideas} />
        <Route path="/teams" component={Teams} />
        <Route path="/mentors" component={Mentors} />
        <Route path="/project" component={ProjectDetails} />
        <Route path="/login" component={Login} />
        <Route path="/onboarding/:role" component={Onboarding} />
        <Route component={NotFound} />
      </Switch>
    </ProtectedRoute>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
