import type { User } from "@shared/schema";

/**
 * Check if a user has completed their profile based on their role
 */
export function isProfileComplete(user: User): boolean {
  if (!user) return false;
  
  // Check if the user has explicitly marked their profile as complete
  if (user.profileCompleted) return true;
  
  // Role-specific completion checks
  switch (user.role) {
    case "eso_admin":
      // ESO admin needs to have completed their organization setup
      // This will be checked via the ESO record in the database
      return false; // Will be updated after ESO profile creation
      
    case "entrepreneur":
      // Entrepreneurs need basic profile info
      return !!(
        user.location &&
        user.interests &&
        user.interests.length > 0 &&
        user.teamPreference
      );
      
    case "mentor":
      // Mentors need basic profile info (mentor profile is separate)
      return !!(
        user.location &&
        user.interests &&
        user.interests.length > 0
      );
      
    case "sponsor":
      // Sponsors need basic profile info (sponsor profile is separate)
      return !!(
        user.location &&
        user.interests &&
        user.interests.length > 0
      );
      
    default:
      return false;
  }
}

/**
 * Get the onboarding route for a user based on their role
 */
export function getOnboardingRoute(user: User): string {
  switch (user.role) {
    case "eso_admin":
      return "/onboarding/eso-admin";
    case "entrepreneur":
      return "/onboarding/entrepreneur";
    case "mentor":
      return "/onboarding/mentor";
    case "sponsor":
      return "/onboarding/sponsor";
    default:
      return "/onboarding/entrepreneur";
  }
}

/**
 * Check if the current route is an onboarding route
 */
export function isOnboardingRoute(path: string): boolean {
  return path.startsWith("/onboarding");
}

/**
 * Check if the current route should be protected (requires completed profile)
 */
export function isProtectedRoute(path: string): boolean {
  const unprotectedRoutes = ["/login", "/api/login", "/api/logout", "/api/callback"];
  const isUnprotected = unprotectedRoutes.some(route => path.startsWith(route));
  const isOnboarding = isOnboardingRoute(path);
  
  return !isUnprotected && !isOnboarding;
}
