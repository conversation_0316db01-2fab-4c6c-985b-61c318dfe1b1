import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import Layout from "@/components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { 
  UserCheck, 
  Search, 
  Filter, 
  Plus, 
  Star, 
  MessageSquare, 
  Calendar,
  Award,
  DollarSign,
  Clock,
  Video,
  Mail,
  Globe
} from "lucide-react";

export default function Mentors() {
  const [searchTerm, setSearchTerm] = useState("");
  const [expertiseFilter, setExpertiseFilter] = useState("all");
  const [showCreateProfile, setShowCreateProfile] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: mentors = [] } = useQuery({
    queryKey: ["/api/mentors"],
  });

  const createMentorProfileMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest("POST", "/api/mentor-profile", data);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Mentor Profile Created!",
        description: "Your mentor profile has been created successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/mentors"] });
      setShowCreateProfile(false);
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to create mentor profile. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Sample mentors for display - in a real app these would come from the database
  const sampleMentors = [
    {
      id: 1,
      user: {
        firstName: "Dr. Michael",
        lastName: "Thompson",
        profileImageUrl: "https://images.unsplash.com/photo-1560250097-0b93528c311a?w=80&h=80&fit=crop&crop=face",
      },
      expertise: ["Food Tech", "Supply Chain", "Sustainability"],
      industries: ["Agriculture", "Logistics", "Clean Tech"],
      experience: "15+ years building sustainable food tech companies. Former VP at FarmDirect, founded two successful agtech startups.",
      availability: "Weekends",
      hourlyRate: 150,
      rating: 4.9,
      sessionsCompleted: 127,
      specialties: ["Go-to-Market", "Fundraising", "Product Strategy"]
    },
    {
      id: 2,
      user: {
        firstName: "Sarah",
        lastName: "Chen",
        profileImageUrl: "https://images.unsplash.com/photo-1494790108755-2616b332c3b7?w=80&h=80&fit=crop&crop=face",
      },
      expertise: ["EdTech", "Product Management", "User Experience"],
      industries: ["Education", "SaaS", "Mobile Apps"],
      experience: "Product leader at top EdTech companies. Led teams at Khan Academy and Coursera, expertise in scaling educational platforms.",
      availability: "Evenings",
      hourlyRate: 120,
      rating: 4.8,
      sessionsCompleted: 89,
      specialties: ["Product Development", "User Research", "Team Building"]
    },
    {
      id: 3,
      user: {
        firstName: "Marcus",
        lastName: "Johnson",
        profileImageUrl: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face",
      },
      expertise: ["HealthTech", "AI/ML", "Regulatory Affairs"],
      industries: ["Healthcare", "Artificial Intelligence", "Medical Devices"],
      experience: "Former head of AI at MedTech Corp. Successfully navigated FDA approvals for 3 medical AI products.",
      availability: "Flexible",
      hourlyRate: 200,
      rating: 5.0,
      sessionsCompleted: 156,
      specialties: ["AI Implementation", "Regulatory Strategy", "Clinical Trials"]
    },
    {
      id: 4,
      user: {
        firstName: "Elena",
        lastName: "Rodriguez",
        profileImageUrl: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=80&h=80&fit=crop&crop=face",
      },
      expertise: ["Climate Tech", "Impact Investing", "Clean Energy"],
      industries: ["Renewable Energy", "Carbon Markets", "ESG"],
      experience: "Impact investor and former climate tech founder. Raised $50M+ for clean energy startups, advisor to UN Climate Fund.",
      availability: "Mornings",
      hourlyRate: 175,
      rating: 4.9,
      sessionsCompleted: 98,
      specialties: ["Impact Measurement", "ESG Strategy", "Climate Finance"]
    },
    {
      id: 5,
      user: {
        firstName: "David",
        lastName: "Kim",
        profileImageUrl: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face",
      },
      expertise: ["Marketplace", "E-commerce", "Growth Marketing"],
      industries: ["E-commerce", "Two-sided Markets", "Consumer Apps"],
      experience: "Growth lead at major marketplace platforms. Scaled user acquisition from 0 to 10M+ users at multiple startups.",
      availability: "Afternoons",
      hourlyRate: 140,
      rating: 4.7,
      sessionsCompleted: 203,
      specialties: ["Growth Hacking", "Marketplace Dynamics", "Performance Marketing"]
    },
    {
      id: 6,
      user: {
        firstName: "Dr. Priya",
        lastName: "Patel",
        profileImageUrl: "https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=80&h=80&fit=crop&crop=face",
      },
      expertise: ["Smart City", "IoT", "Data Analytics"],
      industries: ["Urban Planning", "Government Tech", "Infrastructure"],
      experience: "Smart city architect with 12+ years experience. Led digital transformation initiatives for major metropolitan areas.",
      availability: "Weekdays",
      hourlyRate: 160,
      rating: 4.8,
      sessionsCompleted: 74,
      specialties: ["IoT Architecture", "Data Strategy", "Public-Private Partnerships"]
    }
  ];

  const expertiseAreas = ["all", "Food Tech", "EdTech", "HealthTech", "Climate Tech", "Marketplace", "Smart City"];

  const filteredMentors = sampleMentors.filter(mentor => {
    const matchesSearch = 
      mentor.user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mentor.user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mentor.expertise.some(exp => exp.toLowerCase().includes(searchTerm.toLowerCase())) ||
      mentor.specialties.some(spec => spec.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesExpertise = expertiseFilter === "all" || 
      mentor.expertise.includes(expertiseFilter);
    
    return matchesSearch && matchesExpertise;
  });

  const isUserMentor = user?.role === "mentor";

  return (
    <Layout>
      <div>
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-neutral-900 mb-2">Mentors</h1>
            <p className="text-neutral-600">
              Connect with experienced mentors to accelerate your startup journey.
            </p>
          </div>
          
          {isUserMentor && (
            <Dialog open={showCreateProfile} onOpenChange={setShowCreateProfile}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Mentor Profile
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Create Your Mentor Profile</DialogTitle>
                </DialogHeader>
                <CreateMentorProfileForm 
                  onSubmit={(data) => createMentorProfileMutation.mutate(data)}
                  isLoading={createMentorProfileMutation.isPending}
                />
              </DialogContent>
            </Dialog>
          )}
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
            <Input
              placeholder="Search mentors by name, expertise, or specialty..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={expertiseFilter} onValueChange={setExpertiseFilter}>
            <SelectTrigger className="w-full md:w-48">
              <Filter className="w-4 h-4 mr-2" />
              <SelectValue placeholder="Expertise" />
            </SelectTrigger>
            <SelectContent>
              {expertiseAreas.map(area => (
                <SelectItem key={area} value={area}>
                  {area === "all" ? "All Expertise" : area}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Mentors Grid */}
        {filteredMentors.length === 0 ? (
          <EmptyMentorsState />
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredMentors.map((mentor) => (
              <MentorCard key={mentor.id} mentor={mentor} />
            ))}
          </div>
        )}
      </div>
    </Layout>
  );
}

function EmptyMentorsState() {
  return (
    <div className="text-center py-12">
      <div className="bg-blue-50 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
        <UserCheck className="w-12 h-12 text-blue-600" />
      </div>
      <h3 className="text-xl font-semibold text-neutral-900 mb-2">No mentors found</h3>
      <p className="text-neutral-600 max-w-md mx-auto mb-6">
        Try adjusting your search or filters to find mentors that match your needs.
      </p>
    </div>
  );
}

function MentorCard({ mentor }: { mentor: any }) {
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const { toast } = useToast();

  return (
    <>
    <Card className="relative overflow-hidden hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center space-x-3">
          <Avatar className="w-16 h-16">
            <AvatarImage src={mentor.user.profileImageUrl} alt={`${mentor.user.firstName} ${mentor.user.lastName}`} />
            <AvatarFallback className="text-lg">
              {mentor.user.firstName?.[0]}{mentor.user.lastName?.[0]}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <CardTitle className="text-lg">{mentor.user.firstName} {mentor.user.lastName}</CardTitle>
            <div className="flex items-center space-x-2 mt-1">
              <div className="flex items-center space-x-1">
                <Star className="w-4 h-4 text-amber-500 fill-current" />
                <span className="text-sm font-medium">{mentor.rating}</span>
              </div>
              <span className="text-sm text-neutral-500">•</span>
              <span className="text-sm text-neutral-500">{mentor.sessionsCompleted} sessions</span>
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Expertise Tags */}
        <div className="flex flex-wrap gap-1">
          {mentor.expertise.slice(0, 3).map((exp: string) => (
            <Badge key={exp} variant="secondary" className="text-xs">
              {exp}
            </Badge>
          ))}
          {mentor.expertise.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{mentor.expertise.length - 3} more
            </Badge>
          )}
        </div>

        {/* Experience */}
        <p className="text-sm text-neutral-600 line-clamp-2">
          {mentor.experience}
        </p>

        {/* Specialties */}
        <div>
          <p className="text-xs font-medium text-neutral-700 mb-1">Specialties:</p>
          <div className="flex flex-wrap gap-1">
            {mentor.specialties.slice(0, 2).map((specialty: string) => (
              <Badge key={specialty} variant="outline" className="text-xs">
                {specialty}
              </Badge>
            ))}
          </div>
        </div>

        {/* Availability & Rate */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-1 text-neutral-600">
            <Clock className="w-4 h-4" />
            <span>{mentor.availability}</span>
          </div>
          <div className="flex items-center space-x-1 font-medium">
            <DollarSign className="w-4 h-4" />
            <span>${mentor.hourlyRate}/hr</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-2 pt-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="flex-1"
            onClick={() => setShowMessageModal(true)}
          >
            <MessageSquare className="w-4 h-4 mr-2" />
            Message
          </Button>
          <Button 
            size="sm" 
            className="flex-1 bg-blue-600 hover:bg-blue-700"
            onClick={() => setShowBookingModal(true)}
          >
            <Calendar className="w-4 h-4 mr-2" />
            Book Session
          </Button>
        </div>
      </CardContent>

      {/* Premium Mentor Badge */}
      {mentor.rating >= 4.9 && (
        <div className="absolute top-4 right-4">
          <Badge className="bg-amber-500 text-white">
            <Award className="w-3 h-3 mr-1" />
            Top Mentor
          </Badge>
        </div>
      )}
    </Card>

    {/* Message Modal */}
    <Dialog open={showMessageModal} onOpenChange={setShowMessageModal}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Message {mentor.user.firstName} {mentor.user.lastName}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="subject">Subject</Label>
            <Input
              id="subject"
              placeholder="What would you like to discuss?"
            />
          </div>
          <div>
            <Label htmlFor="message">Message</Label>
            <Textarea
              id="message"
              placeholder="Hi! I'd love to get your advice on..."
              rows={4}
            />
          </div>
          <Button 
            className="w-full" 
            onClick={() => {
              toast({
                title: "Message Sent!",
                description: `Your message has been sent to ${mentor.user.firstName}.`,
              });
              setShowMessageModal(false);
            }}
          >
            Send Message
          </Button>
        </div>
      </DialogContent>
    </Dialog>

    {/* Booking Modal */}
    <Dialog open={showBookingModal} onOpenChange={setShowBookingModal}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Book Session with {mentor.user.firstName} {mentor.user.lastName}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="session-type">Session Type</Label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Select session type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="consultation">1-hour Consultation</SelectItem>
                <SelectItem value="review">Milestone Review</SelectItem>
                <SelectItem value="strategy">Strategy Session</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="preferred-date">Preferred Date</Label>
            <Input
              id="preferred-date"
              type="date"
            />
          </div>
          <div>
            <Label htmlFor="preferred-time">Preferred Time</Label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Select time" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="9am">9:00 AM</SelectItem>
                <SelectItem value="10am">10:00 AM</SelectItem>
                <SelectItem value="11am">11:00 AM</SelectItem>
                <SelectItem value="2pm">2:00 PM</SelectItem>
                <SelectItem value="3pm">3:00 PM</SelectItem>
                <SelectItem value="4pm">4:00 PM</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="session-notes">Session Notes</Label>
            <Textarea
              id="session-notes"
              placeholder="What specific topics would you like to cover?"
              rows={3}
            />
          </div>
          <div className="bg-neutral-50 p-3 rounded-lg">
            <p className="text-sm text-neutral-600">
              <strong>Rate:</strong> ${mentor.hourlyRate}/hour
            </p>
            <p className="text-sm text-neutral-600">
              <strong>Availability:</strong> {mentor.availability}
            </p>
          </div>
          <Button 
            className="w-full" 
            onClick={() => {
              toast({
                title: "Session Booked!",
                description: `Your session with ${mentor.user.firstName} has been scheduled.`,
              });
              setShowBookingModal(false);
            }}
          >
            Book Session
          </Button>
        </div>
      </DialogContent>
    </Dialog>
    </>
  );
}

function CreateMentorProfileForm({ onSubmit, isLoading }: { onSubmit: (data: any) => void; isLoading: boolean }) {
  const [expertise, setExpertise] = useState<string[]>([]);
  const [industries, setIndustries] = useState<string[]>([]);
  const [experience, setExperience] = useState("");
  const [availability, setAvailability] = useState("");
  const [hourlyRate, setHourlyRate] = useState<number | undefined>();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (expertise.length > 0 && experience && availability) {
      onSubmit({
        expertise,
        industries,
        experience,
        availability,
        hourlyRate,
      });
    }
  };

  const expertiseOptions = ["Food Tech", "EdTech", "HealthTech", "Climate Tech", "Marketplace", "Smart City", "AI/ML", "Blockchain"];
  const industryOptions = ["Agriculture", "Education", "Healthcare", "Clean Energy", "E-commerce", "SaaS", "Mobile Apps", "IoT"];

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label>Areas of Expertise</Label>
        <div className="grid grid-cols-2 gap-2 mt-2">
          {expertiseOptions.map(option => (
            <label key={option} className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={expertise.includes(option)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setExpertise([...expertise, option]);
                  } else {
                    setExpertise(expertise.filter(exp => exp !== option));
                  }
                }}
              />
              <span className="text-sm">{option}</span>
            </label>
          ))}
        </div>
      </div>

      <div>
        <Label>Industries</Label>
        <div className="grid grid-cols-2 gap-2 mt-2">
          {industryOptions.map(option => (
            <label key={option} className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={industries.includes(option)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setIndustries([...industries, option]);
                  } else {
                    setIndustries(industries.filter(ind => ind !== option));
                  }
                }}
              />
              <span className="text-sm">{option}</span>
            </label>
          ))}
        </div>
      </div>
      
      <div>
        <Label htmlFor="experience">Experience & Background</Label>
        <Textarea
          id="experience"
          value={experience}
          onChange={(e) => setExperience(e.target.value)}
          placeholder="Describe your professional background, achievements, and what you can help entrepreneurs with..."
          rows={4}
          required
        />
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="availability">Availability</Label>
          <Select value={availability} onValueChange={setAvailability} required>
            <SelectTrigger>
              <SelectValue placeholder="Select availability" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Mornings">Mornings</SelectItem>
              <SelectItem value="Afternoons">Afternoons</SelectItem>
              <SelectItem value="Evenings">Evenings</SelectItem>
              <SelectItem value="Weekends">Weekends</SelectItem>
              <SelectItem value="Weekdays">Weekdays</SelectItem>
              <SelectItem value="Flexible">Flexible</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="hourly-rate">Hourly Rate (USD)</Label>
          <Input
            id="hourly-rate"
            type="number"
            value={hourlyRate || ""}
            onChange={(e) => setHourlyRate(e.target.value ? parseInt(e.target.value) : undefined)}
            placeholder="e.g. 150"
            min="0"
          />
        </div>
      </div>
      
      <Button type="submit" disabled={isLoading} className="w-full">
        {isLoading ? "Creating Profile..." : "Create Mentor Profile"}
      </Button>
    </form>
  );
}