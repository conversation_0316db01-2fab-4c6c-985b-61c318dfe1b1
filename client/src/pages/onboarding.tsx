import { useLocation } from "wouter";
import { useAuth } from "@/hooks/useAuth";
import { useProfileCompletion } from "@/hooks/useProfileCompletion";
import EsoAdminOnboarding from "@/components/onboarding/EsoAdminOnboarding";

export default function Onboarding() {
  const [location, setLocation] = useLocation();
  const { user } = useAuth();
  const { profileCompleted } = useProfileCompletion();

  const handleOnboardingComplete = () => {
    setLocation("/");
  };

  // Extract the role from the URL path
  const role = location.split("/").pop();

  // Show loading only while user authentication is loading
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-neutral-50">
        <div className="text-center">
          <div className="bg-primary text-white p-4 rounded-lg mb-4 inline-block">
            <div className="w-8 h-8 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
          </div>
          <p className="text-neutral-600">Loading...</p>
        </div>
      </div>
    );
  }

  // If profile is already completed, don't show onboarding content
  // ProtectedRoute will handle the redirect, but this prevents any flicker
  if (profileCompleted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-neutral-50">
        <div className="text-center">
          <div className="bg-primary text-white p-4 rounded-lg mb-4 inline-block">
            <div className="w-8 h-8 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
          </div>
          <p className="text-neutral-600">Redirecting...</p>
        </div>
      </div>
    );
  }

  switch (role) {
    case "eso-admin":
      return <EsoAdminOnboarding onComplete={handleOnboardingComplete} />;
    
    case "entrepreneur":
      return (
        <div className="min-h-screen flex items-center justify-center bg-neutral-50">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-neutral-900 mb-4">Entrepreneur Onboarding</h1>
            <p className="text-neutral-600 mb-6">Coming soon...</p>
            <button
              onClick={handleOnboardingComplete}
              className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90"
            >
              Continue to Dashboard
            </button>
          </div>
        </div>
      );
    
    case "mentor":
      return (
        <div className="min-h-screen flex items-center justify-center bg-neutral-50">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-neutral-900 mb-4">Mentor Onboarding</h1>
            <p className="text-neutral-600 mb-6">Coming soon...</p>
            <button
              onClick={handleOnboardingComplete}
              className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90"
            >
              Continue to Dashboard
            </button>
          </div>
        </div>
      );
    
    case "sponsor":
      return (
        <div className="min-h-screen flex items-center justify-center bg-neutral-50">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-neutral-900 mb-4">Sponsor Onboarding</h1>
            <p className="text-neutral-600 mb-6">Coming soon...</p>
            <button
              onClick={handleOnboardingComplete}
              className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90"
            >
              Continue to Dashboard
            </button>
          </div>
        </div>
      );
    
    default:
      return (
        <div className="min-h-screen flex items-center justify-center bg-neutral-50">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-neutral-900 mb-4">Invalid Onboarding Route</h1>
            <p className="text-neutral-600 mb-6">The onboarding route you're trying to access doesn't exist.</p>
            <button
              onClick={() => setLocation("/")}
              className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90"
            >
              Go to Dashboard
            </button>
          </div>
        </div>
      );
  }
}
