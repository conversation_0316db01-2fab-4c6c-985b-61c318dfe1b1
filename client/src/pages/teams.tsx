import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import Layout from "@/components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { 
  Users, 
  Plus, 
  Crown, 
  Calendar, 
  MessageSquare, 
  TrendingUp, 
  UserPlus,
  Settings,
  CheckCircle,
  Clock,
  Lightbulb,
  Target,
  Send,
  Lock,
  Copy,
  Mail,
  Link2,
  X
} from "lucide-react";

export default function Teams() {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: teams = [] } = useQuery({
    queryKey: ["/api/teams/my"],
  });

  const { data: startupIdeas = [] } = useQuery({
    queryKey: ["/api/startup-ideas"],
  });

  const createTeamMutation = useMutation({
    mutationFn: async (data: { name: string; startupIdeaId: number; description: string }) => {
      const response = await apiRequest("POST", "/api/teams", data);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Team Created!",
        description: "Your new team has been created successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/teams/my"] });
      setShowCreateForm(false);
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to create team. Please try again.",
        variant: "destructive",
      });
    },
  });

  return (
    <Layout>
      <div>
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-neutral-900 mb-2">Teams</h1>
            <p className="text-neutral-600">
              Collaborate with talented individuals to bring your startup ideas to life.
            </p>
          </div>
          <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
            <DialogTrigger asChild>
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                Create Team
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Team</DialogTitle>
              </DialogHeader>
              <CreateTeamForm 
                startupIdeas={startupIdeas}
                onSubmit={(data) => createTeamMutation.mutate(data)}
                isLoading={createTeamMutation.isPending}
              />
            </DialogContent>
          </Dialog>
        </div>

        {teams.length === 0 ? (
          <EmptyTeamsState onCreateTeam={() => setShowCreateForm(true)} />
        ) : (
          <TeamsGrid teams={teams} />
        )}
      </div>
    </Layout>
  );
}

function EmptyTeamsState({ onCreateTeam }: { onCreateTeam: () => void }) {
  return (
    <div className="text-center py-12">
      <div className="bg-blue-50 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
        <Users className="w-12 h-12 text-blue-600" />
      </div>
      <h3 className="text-xl font-semibold text-neutral-900 mb-2">Start Your First Team</h3>
      <p className="text-neutral-600 max-w-md mx-auto mb-6">
        Create a team to collaborate on startup ideas, track milestones together, and bring your vision to reality.
      </p>
      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        <Button onClick={onCreateTeam} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="w-4 h-4 mr-2" />
          Create Your First Team
        </Button>
        <Button variant="outline" asChild>
          <a href="/ideas">
            <Lightbulb className="w-4 h-4 mr-2" />
            Browse Startup Ideas
          </a>
        </Button>
      </div>
    </div>
  );
}

function TeamsGrid({ teams }: { teams: any[] }) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
      {teams.map((team) => (
        <TeamCard key={team.id} team={team} />
      ))}
    </div>
  );
}

function TeamCard({ team }: { team: any }) {
  const [showChatModal, setShowChatModal] = useState(false);
  const [showMilestonesModal, setShowMilestonesModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const { toast } = useToast();
  
  const { data: members = [] } = useQuery({
    queryKey: ["/api/teams", team.id, "members"],
  });

  // Mock data for display purposes - replace with real milestone progress data
  const mockProgress = {
    totalMilestones: 8,
    completedMilestones: Math.floor(Math.random() * 4) + 1,
    currentMilestone: "Business Model Canvas",
    nextDeadline: "3 days",
  };

  const progressPercentage = (mockProgress.completedMilestones / mockProgress.totalMilestones) * 100;

  return (
    <>
    <Card className="relative overflow-hidden hover:shadow-lg transition-shadow h-full flex flex-col">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">{team.name}</CardTitle>
          <Button variant="ghost" size="sm" onClick={() => setShowSettingsModal(true)}>
            <Settings className="w-4 h-4" />
          </Button>
        </div>
        <div className="min-h-[48px] flex items-start">
          <p className="text-sm text-neutral-600 line-clamp-2">
            {team.description || "Working together to build an amazing startup"}
          </p>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4 flex-1 flex flex-col">
        {/* Progress Section */}
        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span className="text-neutral-600 font-medium">Progress</span>
            <span className="font-semibold text-neutral-800">{mockProgress.completedMilestones}/{mockProgress.totalMilestones} milestones</span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
          <div className="flex items-center justify-between text-xs text-neutral-500">
            <span>Current: {mockProgress.currentMilestone}</span>
            <span>Due in {mockProgress.nextDeadline}</span>
          </div>
        </div>

        {/* Team Members Preview */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-neutral-700">Team Members</span>
            <Badge variant="secondary" className="bg-green-100 text-green-700 hover:bg-green-100">
              {Array.isArray(members) ? members.length : 0} members
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {Array.isArray(members) && members.slice(0, 3).map((member: any, index: number) => (
                <Avatar key={member.id} className="w-8 h-8 border-2 border-white shadow-sm">
                  <AvatarImage src={`https://images.unsplash.com/photo-${1560250097 + index * 1000}-0b93528c311a?w=32&h=32&fit=crop&crop=face`} />
                  <AvatarFallback className="text-xs bg-blue-100 text-blue-700">
                    {member.role?.charAt(0) || 'M'}
                  </AvatarFallback>
                </Avatar>
              ))}
              {Array.isArray(members) && members.length > 3 && (
                <div className="w-8 h-8 rounded-full bg-neutral-100 border-2 border-white shadow-sm flex items-center justify-center">
                  <span className="text-xs text-neutral-600 font-medium">+{members.length - 3}</span>
                </div>
              )}
              {(!Array.isArray(members) || members.length === 0) && (
                <div className="flex items-center space-x-2 text-neutral-400">
                  <Users className="w-4 h-4" />
                  <span className="text-xs">No members yet</span>
                </div>
              )}
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              className="w-8 h-8 p-0 hover:bg-orange-100 hover:text-orange-600"
              onClick={() => setShowInviteModal(true)}
            >
              <UserPlus className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-3 gap-2 pt-3 mt-auto">
          <Button 
            variant="outline" 
            size="sm" 
            className="hover:bg-neutral-50 px-2"
            onClick={() => setShowChatModal(true)}
          >
            <MessageSquare className="w-4 h-4 mr-1 flex-shrink-0" />
            <span className="truncate">Chat</span>
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            className="hover:bg-neutral-50 px-2"
            onClick={() => setShowMilestonesModal(true)}
          >
            <Target className="w-4 h-4 mr-1 flex-shrink-0" />
            <span className="truncate">Milestones</span>
          </Button>
          <Button 
            size="sm" 
            className="bg-blue-600 hover:bg-blue-700 text-white px-2"
            onClick={() => setShowViewModal(true)}
          >
            <TrendingUp className="w-4 h-4 mr-1 flex-shrink-0" />
            <span className="truncate">View</span>
          </Button>
        </div>
      </CardContent>

      {/* Team Leader Badge */}
      <div className="absolute top-2 right-2">
        <Crown className="w-4 h-4 text-amber-500" />
      </div>
    </Card>

    {/* Team Chat Modal */}
    <Dialog open={showChatModal} onOpenChange={setShowChatModal}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{team.name} - Team Chat</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          {/* Chat Messages Area */}
          <div className="h-80 border rounded-lg p-4 bg-neutral-50 overflow-y-auto">
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <Avatar className="w-8 h-8">
                  <AvatarFallback>JD</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="bg-white p-3 rounded-lg shadow-sm">
                    <p className="text-sm">Welcome to the team chat! Let's collaborate on our startup idea.</p>
                    <span className="text-xs text-neutral-500">2 hours ago</span>
                  </div>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Avatar className="w-8 h-8">
                  <AvatarFallback>SM</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="bg-white p-3 rounded-lg shadow-sm">
                    <p className="text-sm">Great! I've been working on the market research. Should we schedule a call to discuss findings?</p>
                    <span className="text-xs text-neutral-500">1 hour ago</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Message Input */}
          <div className="flex space-x-2">
            <Input 
              placeholder="Type your message..." 
              className="flex-1"
            />
            <Button onClick={() => {
              toast({
                title: "Message Sent!",
                description: "Your message has been sent to the team.",
              });
            }}>
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>

    {/* Milestones Modal */}
    <Dialog open={showMilestonesModal} onOpenChange={setShowMilestonesModal}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>{team.name} - Milestone Progress</DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          {/* Overall Progress */}
          <div className="bg-neutral-50 p-4 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium">Overall Progress</h3>
              <span className="text-sm text-neutral-600">{mockProgress.completedMilestones}/{mockProgress.totalMilestones} completed</span>
            </div>
            <Progress value={progressPercentage} className="h-3" />
          </div>

          {/* Milestone List */}
          <div className="space-y-3">
            {[
              { id: 1, title: "Market Research", status: "completed", dueDate: "Completed" },
              { id: 2, title: "Business Model Canvas", status: "in_progress", dueDate: "Due in 3 days" },
              { id: 3, title: "MVP Development", status: "locked", dueDate: "Not started" },
              { id: 4, title: "User Testing", status: "locked", dueDate: "Not started" },
              { id: 5, title: "Financial Projections", status: "locked", dueDate: "Not started" },
              { id: 6, title: "Pitch Deck", status: "locked", dueDate: "Not started" },
              { id: 7, title: "Funding Strategy", status: "locked", dueDate: "Not started" },
              { id: 8, title: "Go-to-Market Plan", status: "locked", dueDate: "Not started" },
            ].map((milestone) => (
              <div key={milestone.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    milestone.status === "completed" ? "bg-green-100 text-green-600" :
                    milestone.status === "in_progress" ? "bg-blue-100 text-blue-600" :
                    "bg-neutral-100 text-neutral-400"
                  }`}>
                    {milestone.status === "completed" ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : milestone.status === "in_progress" ? (
                      <Clock className="w-4 h-4" />
                    ) : (
                      <Lock className="w-4 h-4" />
                    )}
                  </div>
                  <div>
                    <h4 className="font-medium">{milestone.title}</h4>
                    <p className="text-sm text-neutral-600">{milestone.dueDate}</p>
                  </div>
                </div>
                <Button 
                  size="sm" 
                  variant={milestone.status === "in_progress" ? "default" : "ghost"}
                  disabled={milestone.status === "locked"}
                  onClick={() => {
                    if (milestone.status === "in_progress") {
                      toast({
                        title: "Milestone Updated",
                        description: `Progress saved for ${milestone.title}`,
                      });
                    }
                  }}
                >
                  {milestone.status === "completed" ? "View" : 
                   milestone.status === "in_progress" ? "Update" : "Locked"}
                </Button>
              </div>
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>

    {/* Team View Modal */}
    <Dialog open={showViewModal} onOpenChange={setShowViewModal}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>{team.name} - Team Overview</DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          {/* Team Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold mb-2">Team Information</h3>
                <p className="text-neutral-600 mb-4">{team.description || "Working together to build an amazing startup"}</p>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Created:</span>
                    <span className="text-sm text-neutral-600">
                      {team.createdAt ? new Date(team.createdAt).toLocaleDateString() : 'Recently'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Members:</span>
                    <span className="text-sm text-neutral-600">{Array.isArray(members) ? members.length : 0} members</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">Progress:</span>
                    <span className="text-sm text-neutral-600">{mockProgress.completedMilestones}/{mockProgress.totalMilestones} milestones</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Progress Overview</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Overall Progress</span>
                  <span className="text-sm font-medium">{Math.round(progressPercentage)}%</span>
                </div>
                <Progress value={progressPercentage} className="h-3" />
                <div className="text-sm text-neutral-600">
                  Current: {mockProgress.currentMilestone}
                </div>
              </div>
            </div>
          </div>

          {/* Team Members Section */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Team Members</h3>
              <Button size="sm" variant="outline" onClick={() => setShowInviteModal(true)}>
                <UserPlus className="w-4 h-4 mr-2" />
                Invite Member
              </Button>
            </div>
            
            {Array.isArray(members) && members.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {members.map((member: any) => (
                  <div key={member.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                    <Avatar className="w-10 h-10">
                      <AvatarImage src={member.user?.profileImageUrl} />
                      <AvatarFallback>
                        {member.user?.firstName?.charAt(0) || member.role?.charAt(0) || 'M'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <p className="font-medium">
                        {member.user?.firstName && member.user?.lastName 
                          ? `${member.user.firstName} ${member.user.lastName}`
                          : member.user?.email || 'Team Member'}
                      </p>
                      <p className="text-sm text-neutral-600">{member.role || 'Member'}</p>
                    </div>
                    <Badge variant={member.status === 'active' ? 'default' : 'secondary'}>
                      {member.status || 'Active'}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 border-2 border-dashed border-neutral-200 rounded-lg">
                <Users className="w-12 h-12 text-neutral-400 mx-auto mb-3" />
                <p className="text-neutral-600">No team members yet</p>
                <p className="text-sm text-neutral-500">Invite members to start collaborating</p>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>

    {/* Team Settings Modal */}
    <Dialog open={showSettingsModal} onOpenChange={setShowSettingsModal}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{team.name} - Settings</DialogTitle>
        </DialogHeader>
        <div className="space-y-6">
          {/* Basic Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Team Settings</h3>
            <div className="space-y-3">
              <div>
                <Label htmlFor="team-name-edit">Team Name</Label>
                <Input id="team-name-edit" defaultValue={team.name} />
              </div>
              <div>
                <Label htmlFor="team-description-edit">Description</Label>
                <Textarea 
                  id="team-description-edit" 
                  defaultValue={team.description || ""}
                  placeholder="Describe your team's mission and goals"
                />
              </div>
            </div>
          </div>

          {/* Member Management */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Member Management</h3>
            <div className="space-y-3">
              {Array.isArray(members) && members.length > 0 ? (
                members.map((member: any) => (
                  <div key={member.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Avatar className="w-8 h-8">
                        <AvatarImage src={member.user?.profileImageUrl} />
                        <AvatarFallback>
                          {member.user?.firstName?.charAt(0) || 'M'}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">
                          {member.user?.firstName && member.user?.lastName 
                            ? `${member.user.firstName} ${member.user.lastName}`
                            : member.user?.email || 'Team Member'}
                        </p>
                        <p className="text-sm text-neutral-600">{member.role || 'Member'}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Select defaultValue={member.role || 'member'}>
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="leader">Leader</SelectItem>
                          <SelectItem value="member">Member</SelectItem>
                          <SelectItem value="contributor">Contributor</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                        Remove
                      </Button>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-neutral-600 text-center py-4">No team members to manage</p>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between pt-4 border-t">
            <Button variant="outline" onClick={() => setShowSettingsModal(false)}>
              Cancel
            </Button>
            <div className="space-x-2">
              <Button variant="outline" onClick={() => setShowInviteModal(true)}>
                <UserPlus className="w-4 h-4 mr-2" />
                Invite Members
              </Button>
              <Button>
                Save Changes
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>

    {/* Team Invitation Modal */}
    <Dialog open={showInviteModal} onOpenChange={setShowInviteModal}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Invite Members to {team.name}</DialogTitle>
        </DialogHeader>
        <InviteMembersForm 
          team={team} 
          onClose={() => setShowInviteModal(false)}
        />
      </DialogContent>
    </Dialog>
    </>
  );
}

function CreateTeamForm({ 
  startupIdeas, 
  onSubmit, 
  isLoading 
}: { 
  startupIdeas: any[]; 
  onSubmit: (data: any) => void; 
  isLoading: boolean; 
}) {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [startupIdeaId, setStartupIdeaId] = useState<string>("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (name && startupIdeaId) {
      onSubmit({ 
        name, 
        description, 
        startupIdeaId: parseInt(startupIdeaId) 
      });
      setName("");
      setDescription("");
      setStartupIdeaId("");
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="team-name">Team Name</Label>
        <Input
          id="team-name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Enter your team name"
          required
        />
      </div>
      
      <div>
        <Label htmlFor="startup-idea">Startup Idea</Label>
        <Select value={startupIdeaId} onValueChange={setStartupIdeaId} required>
          <SelectTrigger>
            <SelectValue placeholder="Select a startup idea to work on" />
          </SelectTrigger>
          <SelectContent>
            {startupIdeas.map((idea) => (
              <SelectItem key={idea.id} value={idea.id.toString()}>
                {idea.title}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <div>
        <Label htmlFor="team-description">Team Description (Optional)</Label>
        <Textarea
          id="team-description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Describe your team's mission and goals..."
          rows={3}
        />
      </div>
      
      <Button type="submit" disabled={isLoading} className="w-full">
        {isLoading ? "Creating..." : "Create Team"}
      </Button>
    </form>
  );
}

function InviteMembersForm({ team, onClose }: { team: any; onClose: () => void }) {
  const [inviteMethod, setInviteMethod] = useState<'email' | 'link'>('email');
  const [emails, setEmails] = useState(['']);
  const [inviteMessage, setInviteMessage] = useState('');
  const [inviteLink, setInviteLink] = useState('');
  const { toast } = useToast();

  // Generate invitation link when component mounts
  useEffect(() => {
    const teamId = team.id;
    const inviteToken = Math.random().toString(36).substring(2, 15);
    const baseUrl = window.location.origin;
    setInviteLink(`${baseUrl}/join-team?team=${teamId}&token=${inviteToken}`);
  }, [team.id]);

  const addEmailField = () => {
    setEmails([...emails, '']);
  };

  const removeEmailField = (index: number) => {
    setEmails(emails.filter((_, i) => i !== index));
  };

  const updateEmail = (index: number, value: string) => {
    const newEmails = [...emails];
    newEmails[index] = value;
    setEmails(newEmails);
  };

  const copyInviteLink = async () => {
    try {
      await navigator.clipboard.writeText(inviteLink);
      toast({
        title: "Link Copied!",
        description: "Invitation link has been copied to your clipboard.",
      });
    } catch (err) {
      toast({
        title: "Copy Failed",
        description: "Please manually copy the invitation link.",
        variant: "destructive",
      });
    }
  };

  const sendInvitations = () => {
    const validEmails = emails.filter(email => email.trim() !== '');
    
    if (validEmails.length === 0) {
      toast({
        title: "No Email Addresses",
        description: "Please add at least one email address to send invitations.",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Invitations Sent!",
      description: `Successfully sent ${validEmails.length} invitation${validEmails.length > 1 ? 's' : ''} to join ${team.name}.`,
    });

    onClose();
  };

  return (
    <div className="space-y-6">
      {/* Invite Method Selection */}
      <div className="space-y-3">
        <Label className="text-base font-medium">How would you like to invite members?</Label>
        <div className="grid grid-cols-2 gap-3">
          <Button
            variant={inviteMethod === 'email' ? 'default' : 'outline'}
            onClick={() => setInviteMethod('email')}
            className="justify-start"
          >
            <Mail className="w-4 h-4 mr-2" />
            Email Invitations
          </Button>
          <Button
            variant={inviteMethod === 'link' ? 'default' : 'outline'}
            onClick={() => setInviteMethod('link')}
            className="justify-start"
          >
            <Link2 className="w-4 h-4 mr-2" />
            Share Link
          </Button>
        </div>
      </div>

      {/* Email Invitation Method */}
      {inviteMethod === 'email' && (
        <div className="space-y-4">
          <div>
            <Label htmlFor="invite-emails">Email Addresses</Label>
            <div className="space-y-2 mt-2">
              {emails.map((email, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Input
                    placeholder="Enter email address"
                    value={email}
                    onChange={(e) => updateEmail(index, e.target.value)}
                    type="email"
                    className="flex-1"
                  />
                  {emails.length > 1 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeEmailField(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              ))}
              <Button
                variant="outline"
                size="sm"
                onClick={addEmailField}
                className="w-full"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Another Email
              </Button>
            </div>
          </div>

          <div>
            <Label htmlFor="invite-message">Personal Message (Optional)</Label>
            <Textarea
              id="invite-message"
              placeholder="Add a personal message to your invitation..."
              value={inviteMessage}
              onChange={(e) => setInviteMessage(e.target.value)}
              rows={3}
            />
          </div>
        </div>
      )}

      {/* Link Sharing Method */}
      {inviteMethod === 'link' && (
        <div className="space-y-4">
          <div>
            <Label>Team Invitation Link</Label>
            <p className="text-sm text-neutral-600 mb-3">
              Share this link with anyone you want to invite to your team. The link is valid for 7 days.
            </p>
            <div className="flex items-center space-x-2">
              <Input
                value={inviteLink}
                readOnly
                className="flex-1 font-mono text-sm"
              />
              <Button onClick={copyInviteLink} variant="outline">
                <Copy className="w-4 h-4 mr-2" />
                Copy
              </Button>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">Share via:</h4>
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  const subject = `Join my team: ${team.name}`;
                  const body = `You've been invited to join the ${team.name} team on Traction!\n\nClick the link below to join:\n${inviteLink}`;
                  window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
                }}
              >
                <Mail className="w-4 h-4 mr-1" />
                Email
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={copyInviteLink}
              >
                <Copy className="w-4 h-4 mr-1" />
                Copy Link
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-between pt-4 border-t">
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        {inviteMethod === 'email' ? (
          <Button onClick={sendInvitations}>
            <Send className="w-4 h-4 mr-2" />
            Send Invitations
          </Button>
        ) : (
          <Button onClick={onClose}>
            Done
          </Button>
        )}
      </div>
    </div>
  );
}