import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useLocation } from "wouter";
import Layout from "@/components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  ArrowLeft,
  Users, 
  Calendar,
  Target,
  TrendingUp,
  MessageSquare,
  CheckCircle,
  Clock,
  Lock,
  ExternalLink,
  Edit,
  Plus,
  FileText,
  Lightbulb
} from "lucide-react";

export default function ProjectDetails() {
  const [location, navigate] = useLocation();
  const urlParams = new URLSearchParams(window.location.search);
  const projectId = urlParams.get('id');
  
  // Get all startup ideas and user teams
  const { data: startupIdeas = [], isLoading: projectLoading } = useQuery({
    queryKey: ["/api/startup-ideas"],
  });

  const { data: teams = [] } = useQuery({
    queryKey: ["/api/teams/my"],
  });

  // Find project by ID, then by team association
  let project = null;
  if (Array.isArray(startupIdeas) && projectId) {
    // Try exact ID match first
    project = startupIdeas.find((idea: any) => idea.id.toString() === projectId);
    
    // If no exact match and we have teams data, find project by team association
    if (!project && Array.isArray(teams) && teams.length > 0) {
      const userTeam = teams.find((team: any) => team.startupIdeaId.toString() === projectId);
      if (userTeam) {
        project = startupIdeas.find((idea: any) => idea.id === userTeam.startupIdeaId);
      }
    }
    
    // Fallback: if user has teams but no exact project match, use their first team's idea
    if (!project && Array.isArray(teams) && teams.length > 0) {
      const firstTeam = teams[0];
      project = startupIdeas.find((idea: any) => idea.id === firstTeam.startupIdeaId);
    }
  }

  // Get milestone progress
  const { data: milestoneProgress = [] } = useQuery({
    queryKey: ["/api/milestone-progress/my"],
  });

  if (projectLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-neutral-600">Loading project details...</p>
          </div>
        </div>
      </Layout>
    );
  }

  // Debug logging - remove in production
  console.log('Debug info:', {
    projectId,
    startupIdeas: Array.isArray(startupIdeas) ? startupIdeas.map(idea => ({ id: idea.id, title: idea.title })) : 'not array',
    project
  });

  if (!project) {
    return (
      <Layout>
        <div className="text-center py-16">
          <h2 className="text-2xl font-bold text-neutral-800">Project Not Found</h2>
          <p className="text-neutral-600 mt-2">The project you're looking for doesn't exist.</p>
          <p className="text-xs text-neutral-500 mt-2">
            Project ID: {projectId} | Available ideas: {Array.isArray(startupIdeas) ? startupIdeas.length : 0}
          </p>
          <Button onClick={() => navigate("/")} className="mt-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Button>
        </div>
      </Layout>
    );
  }

  // Mock milestone data for display
  const milestones = [
    { id: 1, title: "Market Research", status: "completed", dueDate: "Completed", description: "Analyze target market and competition" },
    { id: 2, title: "Business Model Canvas", status: "completed", dueDate: "Completed", description: "Define value proposition and revenue streams" },
    { id: 3, title: "MVP Planning", status: "completed", dueDate: "Completed", description: "Plan minimum viable product features" },
    { id: 4, title: "Technical Architecture", status: "in_progress", dueDate: "Due in 5 days", description: "Design system architecture and tech stack" },
    { id: 5, title: "MVP Development", status: "locked", dueDate: "Not started", description: "Build core product features" },
    { id: 6, title: "User Testing", status: "locked", dueDate: "Not started", description: "Test with real users and gather feedback" },
    { id: 7, title: "Financial Projections", status: "locked", dueDate: "Not started", description: "Create detailed financial models" },
    { id: 8, title: "Pitch Deck", status: "locked", dueDate: "Not started", description: "Prepare investor presentation" },
    { id: 9, title: "Funding Strategy", status: "locked", dueDate: "Not started", description: "Plan fundraising approach" },
    { id: 10, title: "Go-to-Market Plan", status: "locked", dueDate: "Not started", description: "Define launch and marketing strategy" },
    { id: 11, title: "Legal & Compliance", status: "locked", dueDate: "Not started", description: "Handle legal requirements" },
    { id: 12, title: "Launch Preparation", status: "locked", dueDate: "Not started", description: "Final preparations for market launch" },
  ];

  const completedMilestones = milestones.filter(m => m.status === "completed").length;
  const progressPercentage = (completedMilestones / milestones.length) * 100;

  const currentTeam = Array.isArray(teams) ? teams.find((team: any) => team.startupIdeaId.toString() === projectId) : null;

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => navigate("/")}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-neutral-800">{project.title}</h1>
              <p className="text-neutral-600 mt-1">{project.description}</p>
            </div>
          </div>
          <Button variant="outline" size="sm">
            <Edit className="w-4 h-4 mr-2" />
            Edit Project
          </Button>
        </div>

        {/* Project Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="w-5 h-5 text-blue-600" />
              <span>Project Overview</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-3">
                <Users className="w-5 h-5 text-neutral-500" />
                <div>
                  <p className="font-medium">{currentTeam ? '4' : '1'} team member{currentTeam ? 's' : ''}</p>
                  <p className="text-sm text-neutral-600">Active contributors</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Calendar className="w-5 h-5 text-neutral-500" />
                <div>
                  <p className="font-medium">Started 3 weeks ago</p>
                  <p className="text-sm text-neutral-600">Project timeline</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <TrendingUp className="w-5 h-5 text-neutral-500" />
                <div>
                  <p className="font-medium">{completedMilestones} of {milestones.length} milestones</p>
                  <p className="text-sm text-neutral-600">Overall progress</p>
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Overall Progress</span>
                <span className="text-sm text-neutral-600">{Math.round(progressPercentage)}%</span>
              </div>
              <Progress value={progressPercentage} className="h-3" />
            </div>

            <div className="flex flex-wrap gap-2">
              {project.tags?.map((tag: string) => (
                <Badge key={tag} variant="secondary">{tag}</Badge>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="milestones">Milestones</TabsTrigger>
            <TabsTrigger value="team">Team</TabsTrigger>
            <TabsTrigger value="research">Research</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <h3 className="font-semibold">Completed Tasks</h3>
                  </div>
                  <p className="text-2xl font-bold text-green-600">{completedMilestones}</p>
                  <p className="text-sm text-neutral-600">Out of {milestones.length} total</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2 mb-2">
                    <Clock className="w-5 h-5 text-blue-600" />
                    <h3 className="font-semibold">In Progress</h3>
                  </div>
                  <p className="text-2xl font-bold text-blue-600">
                    {milestones.filter(m => m.status === "in_progress").length}
                  </p>
                  <p className="text-sm text-neutral-600">Active milestones</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2 mb-2">
                    <Users className="w-5 h-5 text-purple-600" />
                    <h3 className="font-semibold">Team Size</h3>
                  </div>
                  <p className="text-2xl font-bold text-purple-600">{currentTeam ? '4' : '1'}</p>
                  <p className="text-sm text-neutral-600">Active members</p>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3 p-3 bg-neutral-50 rounded-lg">
                    <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                    <div>
                      <p className="font-medium">MVP Planning completed</p>
                      <p className="text-sm text-neutral-600">2 days ago</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-3 bg-neutral-50 rounded-lg">
                    <Users className="w-5 h-5 text-blue-600 mt-0.5" />
                    <div>
                      <p className="font-medium">Team member joined the project</p>
                      <p className="text-sm text-neutral-600">1 week ago</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-3 bg-neutral-50 rounded-lg">
                    <FileText className="w-5 h-5 text-purple-600 mt-0.5" />
                    <div>
                      <p className="font-medium">Business Model Canvas updated</p>
                      <p className="text-sm text-neutral-600">1 week ago</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="milestones" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Milestone Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {milestones.map((milestone) => (
                    <div key={milestone.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                          milestone.status === "completed" ? "bg-green-100 text-green-600" :
                          milestone.status === "in_progress" ? "bg-blue-100 text-blue-600" :
                          "bg-neutral-100 text-neutral-400"
                        }`}>
                          {milestone.status === "completed" ? (
                            <CheckCircle className="w-5 h-5" />
                          ) : milestone.status === "in_progress" ? (
                            <Clock className="w-5 h-5" />
                          ) : (
                            <Lock className="w-5 h-5" />
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium">{milestone.title}</h4>
                          <p className="text-sm text-neutral-600">{milestone.description}</p>
                          <p className="text-xs text-neutral-500 mt-1">{milestone.dueDate}</p>
                        </div>
                      </div>
                      <Button 
                        size="sm" 
                        variant={milestone.status === "in_progress" ? "default" : "ghost"}
                        disabled={milestone.status === "locked"}
                      >
                        {milestone.status === "completed" ? "View" : 
                         milestone.status === "in_progress" ? "Update" : "Locked"}
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="team" className="space-y-6">
            {currentTeam ? (
              <Card>
                <CardHeader>
                  <CardTitle>Team: {currentTeam.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {[
                        { name: "John Doe", role: "Project Leader", avatar: "JD", status: "online" },
                        { name: "Sarah Miller", role: "Developer", avatar: "SM", status: "online" },
                        { name: "Mike Chen", role: "Designer", avatar: "MC", status: "away" },
                        { name: "Lisa Park", role: "Marketing", avatar: "LP", status: "offline" },
                      ].map((member, index) => (
                        <div key={index} className="flex items-center space-x-3 p-3 border rounded-lg">
                          <Avatar>
                            <AvatarFallback>{member.avatar}</AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <h4 className="font-medium">{member.name}</h4>
                            <p className="text-sm text-neutral-600">{member.role}</p>
                          </div>
                          <div className={`w-3 h-3 rounded-full ${
                            member.status === "online" ? "bg-green-500" :
                            member.status === "away" ? "bg-yellow-500" : "bg-neutral-300"
                          }`}></div>
                        </div>
                      ))}
                    </div>
                    <Button className="w-full" variant="outline">
                      <Plus className="w-4 h-4 mr-2" />
                      Invite Team Member
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>Team</CardTitle>
                </CardHeader>
                <CardContent className="text-center py-8">
                  <Users className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                  <h3 className="font-medium text-neutral-800 mb-2">No team yet</h3>
                  <p className="text-neutral-600 mb-4">Create a team to collaborate on this project</p>
                  <Button onClick={() => navigate("/teams")}>
                    <Plus className="w-4 h-4 mr-2" />
                    Create Team
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="research" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Market Research</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h4 className="font-medium mb-2">Market Size</h4>
                    <p className="text-neutral-600">The global food delivery market is valued at $130 billion and growing at 11% annually. The sustainable food segment represents a $15 billion opportunity.</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Target Audience</h4>
                    <p className="text-neutral-600">Environmentally conscious urban consumers aged 25-45 who prioritize local, sustainable food options and are willing to pay premium prices.</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Competitive Analysis</h4>
                    <div className="space-y-2">
                      <div className="p-3 bg-neutral-50 rounded-lg">
                        <h5 className="font-medium">Direct Competitors</h5>
                        <p className="text-sm text-neutral-600">Local farm delivery services, sustainable food platforms</p>
                      </div>
                      <div className="p-3 bg-neutral-50 rounded-lg">
                        <h5 className="font-medium">Indirect Competitors</h5>
                        <p className="text-sm text-neutral-600">Traditional food delivery apps, grocery stores, farmers markets</p>
                      </div>
                    </div>
                  </div>
                  
                  <Button variant="outline">
                    <FileText className="w-4 h-4 mr-2" />
                    View Full Research Report
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
}