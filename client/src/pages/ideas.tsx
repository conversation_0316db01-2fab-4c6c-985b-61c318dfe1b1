import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useLocation } from "wouter";
import Layout from "@/components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { 
  Lightbulb, 
  Search, 
  Filter, 
  Plus, 
  Users, 
  Star, 
  Clock,
  TrendingUp,
  Globe,
  Zap
} from "lucide-react";

export default function Ideas() {
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: startupIdeas = [] } = useQuery({
    queryKey: ["/api/startup-ideas"],
  });

  const { data: teams = [] } = useQuery({
    queryKey: ["/api/teams/my"],
  });

  const createIdeaMutation = useMutation({
    mutationFn: async (data: { title: string; description: string; category: string }) => {
      const response = await apiRequest("POST", "/api/startup-ideas", data);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Idea Created!",
        description: "Your startup idea has been added successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/startup-ideas"] });
      setShowCreateForm(false);
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to create startup idea. Please try again.",
        variant: "destructive",
      });
    },
  });

  const createTeamMutation = useMutation({
    mutationFn: async (data: { name: string; startupIdeaId: number; description: string }) => {
      const response = await apiRequest("POST", "/api/teams", data);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Team Created!",
        description: "You've successfully started working on this idea. Check your dashboard for milestones.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/teams/my"] });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to start project. Please try again.",
        variant: "destructive",
      });
    },
  });

  const categories = ["all", "Food Tech", "EdTech", "Smart City", "HealthTech", "Marketplace", "Climate Tech"];

  // Enhanced startup ideas with additional display properties
  const enhancedIdeas = startupIdeas.map(idea => ({
    ...idea,
    popularity: Math.floor(Math.random() * 20) + 80, // 80-99%
    difficulty: ["Easy", "Medium", "Hard"][Math.floor(Math.random() * 3)],
    estimatedTime: ["3 months", "4 months", "5 months", "6 months", "8 months", "10 months", "12 months"][Math.floor(Math.random() * 7)],
    tags: getTagsForCategory(idea.category),
  }));

  const filteredIdeas = enhancedIdeas.filter(idea => {
    const matchesSearch = idea.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         idea.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         idea.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = categoryFilter === "all" || idea.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  function getTagsForCategory(category: string): string[] {
    const categoryTags: Record<string, string[]> = {
      "Food Tech": ["AI", "Sustainability", "Food", "Delivery"],
      "EdTech": ["Education", "Community", "Skills", "Learning"],
      "Smart City": ["IoT", "Smart City", "Analytics", "Sustainability"],
      "HealthTech": ["AI", "Health", "Wellness", "Medical"],
      "Marketplace": ["Blockchain", "Marketplace", "Commerce", "Trade"],
      "Climate Tech": ["Climate", "Environment", "Green", "Sustainability"],
    };
    return categoryTags[category] || ["Innovation", "Technology", "Business"];
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Easy": return "bg-green-500";
      case "Medium": return "bg-amber-500";
      case "Hard": return "bg-red-500";
      default: return "bg-gray-500";
    }
  };

  // Check if user already has a project for this idea
  const hasProjectForIdea = (ideaId: any) => {
    return Array.isArray(teams) && teams.some((team: any) => team.startupIdeaId === ideaId);
  };

  const handleSelectIdea = (idea: any) => {
    createTeamMutation.mutate({
      name: `${idea.title} Team`,
      startupIdeaId: idea.id,
      description: `Working on: ${idea.description}`,
    });
  };

  return (
    <Layout>
      <div>
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-neutral-900 mb-2">Startup Ideas</h1>
          <p className="text-neutral-600">
            Discover AI-curated startup ideas tailored to market opportunities and your interests.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
            <Input
              placeholder="Search ideas, categories, or tags..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-full md:w-48">
              <Filter className="w-4 h-4 mr-2" />
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map(category => (
                <SelectItem key={category} value={category}>
                  {category === "all" ? "All Categories" : category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
            <DialogTrigger asChild>
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                Submit Idea
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Submit Your Startup Idea</DialogTitle>
              </DialogHeader>
              <CreateIdeaForm 
                onSubmit={(data) => createIdeaMutation.mutate(data)}
                isLoading={createIdeaMutation.isPending}
              />
            </DialogContent>
          </Dialog>
        </div>

        {/* Ideas Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredIdeas.map((idea) => (
            <Card key={idea.id} className="relative overflow-hidden">
              {idea.isPremium && (
                <div className="absolute top-4 right-4">
                  <Badge className="bg-amber-500 text-white">
                    <Star className="w-3 h-3 mr-1" />
                    Premium
                  </Badge>
                </div>
              )}
              
              <CardHeader>
                <div className="flex items-start justify-between">
                  <CardTitle className="text-lg">{idea.title}</CardTitle>
                  <div className="flex items-center space-x-1 text-sm text-neutral-500">
                    <TrendingUp className="w-4 h-4" />
                    <span>{idea.popularity}%</span>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <p className="text-neutral-600 text-sm mb-4 line-clamp-3">
                  {idea.description}
                </p>
                
                <div className="space-y-3 mb-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-neutral-500">Difficulty:</span>
                    <Badge className={`${getDifficultyColor(idea.difficulty)} text-white`}>
                      {idea.difficulty}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-neutral-500">Estimated Time:</span>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>{idea.estimatedTime}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-neutral-500">Category:</span>
                    <Badge variant="outline">{idea.category}</Badge>
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-1 mb-4">
                  {idea.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
                
                <div className="flex space-x-2">
                  {hasProjectForIdea(idea.id) ? (
                    <Button 
                      onClick={() => navigate(`/project?id=${idea.id}`)}
                      className="flex-1 bg-green-600 hover:bg-green-700"
                    >
                      <TrendingUp className="w-4 h-4 mr-2" />
                      View Project
                    </Button>
                  ) : (
                    <Button 
                      onClick={() => handleSelectIdea(idea)}
                      disabled={createTeamMutation.isPending}
                      className="flex-1 bg-blue-600 hover:bg-blue-700"
                    >
                      <Zap className="w-4 h-4 mr-2" />
                      Start Project
                    </Button>
                  )}
                  <Button variant="outline" size="sm">
                    <Globe className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredIdeas.length === 0 && (
          <div className="text-center py-12">
            <Lightbulb className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-neutral-900 mb-2">No ideas found</h3>
            <p className="text-neutral-600 mb-4">
              Try adjusting your search or filters to find relevant startup ideas.
            </p>
            <Button onClick={() => setShowCreateForm(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Submit Your Own Idea
            </Button>
          </div>
        )}
      </div>
    </Layout>
  );
}

function CreateIdeaForm({ onSubmit, isLoading }: { onSubmit: (data: any) => void; isLoading: boolean }) {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [category, setCategory] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (title && description && category) {
      onSubmit({ title, description, category });
      setTitle("");
      setDescription("");
      setCategory("");
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="title">Idea Title</Label>
        <Input
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter your startup idea title"
          required
        />
      </div>
      
      <div>
        <Label htmlFor="category">Category</Label>
        <Select value={category} onValueChange={setCategory} required>
          <SelectTrigger>
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Food Tech">Food Tech</SelectItem>
            <SelectItem value="EdTech">EdTech</SelectItem>
            <SelectItem value="HealthTech">HealthTech</SelectItem>
            <SelectItem value="Climate Tech">Climate Tech</SelectItem>
            <SelectItem value="Smart City">Smart City</SelectItem>
            <SelectItem value="Marketplace">Marketplace</SelectItem>
            <SelectItem value="Other">Other</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Describe your startup idea in detail..."
          rows={4}
          required
        />
      </div>
      
      <Button type="submit" disabled={isLoading} className="w-full">
        {isLoading ? "Submitting..." : "Submit Idea"}
      </Button>
    </form>
  );
}