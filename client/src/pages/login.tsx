import { useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useLocation } from "wouter";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Rocket } from "lucide-react";

export default function Login() {
  const { isAuthenticated, isLoading } = useAuth();
  const [, setLocation] = useLocation();

  useEffect(() => {
    if (isAuthenticated) {
      setLocation("/");
    }
  }, [isAuthenticated, setLocation]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-neutral-50">
        <div className="text-center">
          <div className="bg-primary text-white p-4 rounded-lg mb-4 inline-block">
            <Rocket className="w-8 h-8 animate-pulse" />
          </div>
          <p className="text-neutral-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-neutral-50">
      <Card className="w-full max-w-md mx-4">
        <CardHeader className="text-center">
          <div className="bg-primary text-white p-4 rounded-lg mb-4 inline-block">
            <Rocket className="w-8 h-8" />
          </div>
          <CardTitle className="text-2xl">Welcome to Traction</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-neutral-600">
            Support aspiring entrepreneurs through structured milestone-based startup development
          </p>
          <p className="text-sm text-neutral-500">
            by Skills Gap Advocate Inc.
          </p>
          <Button asChild className="w-full">
            <a href="/api/login">Sign In to Continue</a>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
