import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { Upload, X, FileText, Image as ImageIcon } from "lucide-react";

interface MilestoneUploadModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  milestone: any;
}

export default function MilestoneUploadModal({
  open,
  onOpenChange,
  milestone,
}: MilestoneUploadModalProps) {
  const [description, setDescription] = useState("");
  const [files, setFiles] = useState<File[]>([]);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const updateProgressMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      const response = await apiRequest(
        "POST",
        "/api/milestone-progress",
        formData,
        {},
      );
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Progress Submitted!",
        description: "Your mentor will review and provide feedback soon.",
      });
      queryClient.invalidateQueries({
        queryKey: ["/api/milestone-progress/my"],
      });
      onOpenChange(false);
      setDescription("");
      setFiles([]);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to submit progress. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || []);
    setFiles((prev) => [...prev, ...selectedFiles]);
  };

  const removeFile = (index: number) => {
    setFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = () => {
    if (!milestone || !description.trim()) {
      toast({
        title: "Error",
        description: "Please provide a progress description.",
        variant: "destructive",
      });
      return;
    }

    const formData = new FormData();

    // Append text fields
    formData.append("milestoneId", milestone.id.toString());
    formData.append("progressDescription", description);
    formData.append("status", "under_review");

    // Append files
    // The key 'files' must match what your backend API expects from Multer.
    files.forEach((file) => {
      formData.append("files", file);
    });

    updateProgressMutation.mutate(formData);
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    if (["jpg", "jpeg", "png", "gif", "webp"].includes(extension || "")) {
      return <ImageIcon className="w-4 h-4 text-blue-500" />;
    }
    return <FileText className="w-4 h-4 text-red-500" />;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Upload Milestone Progress</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Label htmlFor="description">Progress Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder={`Describe your progress on the ${milestone?.title} milestone...`}
              rows={4}
              className="mt-2"
            />
          </div>

          <div>
            <Label>Upload Files</Label>
            <div className="border-2 border-dashed border-neutral-300 rounded-lg p-6 text-center mt-2">
              <Upload className="w-8 h-8 text-neutral-400 mx-auto mb-2" />
              <p className="text-sm text-neutral-600">
                Drop files here or click to browse
              </p>
              <p className="text-xs text-neutral-500 mt-1">
                PDF, DOC, Images up to 10MB
              </p>
              <input
                type="file"
                multiple
                accept=".pdf,.doc,.docx,.png,.jpg,.jpeg,.gif"
                onChange={handleFileChange}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
            </div>
          </div>

          {files.length > 0 && (
            <div className="space-y-2">
              {files.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-2 bg-neutral-50 rounded"
                >
                  <div className="flex items-center space-x-2">
                    {getFileIcon(file.name)}
                    <span className="text-sm text-neutral-700">
                      {file.name}
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(index)}
                    className="text-neutral-400 hover:text-red-500"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="flex space-x-3 mt-6">
          <Button
            variant="outline"
            className="flex-1"
            onClick={() => onOpenChange(false)}
            disabled={updateProgressMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            className="flex-1 bg-blue-600 hover:bg-blue-700"
            onClick={handleSubmit}
            disabled={updateProgressMutation.isPending || !description.trim()}
          >
            {updateProgressMutation.isPending
              ? "Submitting..."
              : "Submit Progress"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
