import { useEffect } from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/hooks/useAuth";
import { useProfileCompletion } from "@/hooks/useProfileCompletion";
import { isProtectedRoute, isOnboardingRoute } from "@/lib/profileUtils";

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export default function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { isAuthenticated } = useAuth();
  const { profileCompleted, onboardingRoute, isLoading } = useProfileCompletion();
  const [location, setLocation] = useLocation();

  useEffect(() => {
    // Skip all redirects while still loading
    if (isLoading) return;

    // If not authenticated, redirect to login (unless already on login page)
    if (!isAuthenticated && !location.startsWith("/login")) {
      setLocation("/login");
      return;
    }

    // If authenticated but profile is not completed and user is on a protected route
    if (isAuthenticated && !profileCompleted && isProtectedRoute(location)) {
      setLocation(onboardingRoute);
      return;
    }

    // If authenticated and profile is completed and user is on onboarding route, redirect to dashboard
    if (isAuthenticated && profileCompleted && isOnboardingRoute(location)) {
      setLocation("/");
      return;
    }
  }, [isAuthenticated, profileCompleted, location, onboardingRoute, setLocation, isLoading]);

  // Show loading while checking authentication and profile status
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-neutral-50">
        <div className="text-center">
          <div className="bg-primary text-white p-4 rounded-lg mb-4 inline-block">
            <div className="w-8 h-8 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
          </div>
          <p className="text-neutral-600">Loading...</p>
        </div>
      </div>
    );
  }

  // If not authenticated, show login prompt
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-neutral-50">
        <div className="text-center">
          <div className="bg-primary text-white p-4 rounded-lg mb-4 inline-block">
            <div className="w-8 h-8">🚀</div>
          </div>
          <h1 className="text-2xl font-bold text-neutral-900 mb-2">Welcome to Traction</h1>
          <p className="text-neutral-600 mb-6">
            Support aspiring entrepreneurs through structured milestone-based startup development
          </p>
          <button
            onClick={() => setLocation("/login")}
            className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90"
          >
            Sign In to Continue
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
