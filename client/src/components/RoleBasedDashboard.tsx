import { useAuth } from "@/hooks/useAuth";
import EntrepreneurDashboard from "./EntrepreneurDashboard";

export default function RoleBasedDashboard() {
  const { user } = useAuth();

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-neutral-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  switch (user.role) {
    case "entrepreneur":
      return <EntrepreneurDashboard />;
    case "eso_admin":
      return <div className="text-center py-12"><p>ESO Admin Dashboard - Coming Soon</p></div>;
    case "mentor":
      return <div className="text-center py-12"><p>Mentor Dashboard - Coming Soon</p></div>;
    case "sponsor":
      return <div className="text-center py-12"><p>Sponsor Dashboard - Coming Soon</p></div>;
    default:
      return <EntrepreneurDashboard />;
  }
}
