import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { useLocation } from "wouter";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import MilestoneUploadModal from "./MilestoneUploadModal";
import { 
  Upload, 
  ExternalLink, 
  Users, 
  Calendar, 
  Lightbulb, 
  UserPlus, 
  UserCheck, 
  BarChart3, 
  CheckCircle, 
  Clock, 
  Lock,
  MessageSquare
} from "lucide-react";

export default function EntrepreneurDashboard() {
  const { user } = useAuth();
  const [, navigate] = useLocation();
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [selectedMilestone, setSelectedMilestone] = useState<any>(null);

  const { data: teams = [] } = useQuery({
    queryKey: ["/api/teams/my"],
  });

  const { data: milestoneProgress = [] } = useQuery({
    queryKey: ["/api/milestone-progress/my"],
  });

  // Get startup ideas to find a real project ID
  const { data: startupIdeas = [] } = useQuery({
    queryKey: ["/api/startup-ideas"],
  });

  // Find the first project the user has a team for, or use the first available idea
  const userProjectIdea = Array.isArray(teams) && Array.isArray(startupIdeas) 
    ? startupIdeas.find((idea: any) => teams.some((team: any) => team.startupIdeaId === idea.id))
    : null;
  
  const fallbackIdea = Array.isArray(startupIdeas) && startupIdeas.length > 0 ? startupIdeas[0] : null;
  const projectIdea = userProjectIdea || fallbackIdea;

  const currentProject = projectIdea ? {
    id: projectIdea.id,
    name: projectIdea.title,
    description: projectIdea.description,
    teamMembers: 4,
    startedDate: "3 weeks ago",
    progress: 33,
    completedMilestones: 4,
    totalMilestones: 12,
  } : {
    id: 1,
    name: "EcoDelivery",
    description: "AI-powered sustainable food delivery platform connecting local farms with urban consumers",
    teamMembers: 4,
    startedDate: "3 weeks ago",
    progress: 33,
    completedMilestones: 4,
    totalMilestones: 12,
  };

  const milestones = [
    {
      id: 1,
      title: "Market Research",
      description: "Analyze target market and competition",
      status: "completed",
      completedDate: "2 days ago",
      order: 1,
    },
    {
      id: 2,
      title: "Business Model Canvas",
      description: "Create comprehensive business model canvas",
      status: "in_progress",
      dueDate: "5 days",
      order: 2,
    },
    {
      id: 3,
      title: "MVP Development",
      description: "Build minimum viable product",
      status: "locked",
      order: 3,
    },
    {
      id: 4,
      title: "User Testing",
      description: "Conduct user testing and feedback",
      status: "locked",
      order: 4,
    },
  ];

  const teamMembers = [
    {
      id: 1,
      name: "Alex Chen",
      role: "Product Lead",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&h=40",
      status: "online",
    },
    {
      id: 2,
      name: "Sarah Johnson",
      role: "Developer",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b332c3b7?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&h=40",
      status: "online",
    },
    {
      id: 3,
      name: "Marcus Rivera",
      role: "Marketing",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&h=40",
      status: "away",
    },
    {
      id: 4,
      name: "Priya Patel",
      role: "Business Development",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&h=40",
      status: "offline",
    },
  ];

  const mentor = {
    name: "Dr. Michael Thompson",
    title: "Startup Mentor • Food Tech Expert",
    avatar: "https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=48&h=48",
    experience: "Specialized in sustainable food systems and marketplace platforms. 15+ years experience.",
    recentFeedback: "Great progress on market research. Consider focusing on urban density metrics for delivery optimization.",
    feedbackTime: "2 hours ago",
  };

  const handleMilestoneUpload = (milestone: any) => {
    setSelectedMilestone(milestone);
    setUploadModalOpen(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-500";
      case "in_progress":
        return "bg-blue-500";
      case "locked":
        return "bg-gray-400";
      default:
        return "bg-gray-400";
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-500 text-white">Completed</Badge>;
      case "in_progress":
        return <Badge className="bg-amber-500 text-white">In Progress</Badge>;
      case "locked":
        return <Badge className="bg-gray-400 text-white">Locked</Badge>;
      default:
        return <Badge className="bg-gray-400 text-white">Future</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4" />;
      case "in_progress":
        return <span className="text-sm font-semibold">2</span>;
      case "locked":
        return <Lock className="w-4 h-4" />;
      default:
        return <span className="text-sm font-semibold">?</span>;
    }
  };

  return (
    <div>
      {/* Dashboard Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-neutral-900 mb-2">
          Welcome back, {user?.firstName}!
        </h1>
        <p className="text-neutral-600">
          Continue building your startup journey with structured milestones and expert guidance.
        </p>
      </div>

      {/* Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Current Project Card */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Current Project</CardTitle>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => navigate(`/project?id=${currentProject.id}`)}
              >
                <ExternalLink className="w-4 h-4" />
              </Button>
            </CardHeader>
            <CardContent>
              <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-4 mb-4">
                <h3 className="font-semibold text-neutral-900 mb-2">{currentProject.name}</h3>
                <p className="text-sm text-neutral-600 mb-3">{currentProject.description}</p>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-blue-600" />
                    <span className="text-sm text-neutral-600">{currentProject.teamMembers} team members</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-neutral-600">Started {currentProject.startedDate}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-neutral-700">Overall Progress</span>
                  <span className="text-sm text-neutral-600">
                    {currentProject.completedMilestones} of {currentProject.totalMilestones} milestones
                  </span>
                </div>
                <Progress value={currentProject.progress} className="h-2" />

                {/* Current Milestone */}
                <div className="border border-blue-200 rounded-lg p-4 bg-blue-50">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-neutral-900">Current: Business Model Canvas</h4>
                    <Badge className="bg-amber-500 text-white">In Progress</Badge>
                  </div>
                  <p className="text-sm text-neutral-600 mb-3">
                    Complete your business model canvas including value propositions, customer segments, and revenue streams.
                  </p>
                  <div className="flex items-center justify-between">
                    <Button 
                      className="bg-blue-600 hover:bg-blue-700"
                      onClick={() => handleMilestoneUpload(milestones.find(m => m.status === "in_progress"))}
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Upload Progress
                    </Button>
                    <span className="text-xs text-neutral-500">Due in 5 days</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button asChild className="w-full bg-blue-600 hover:bg-blue-700 text-left justify-start">
              <a href="/ideas">
                <Lightbulb className="w-4 h-4 mr-3" />
                Browse New Ideas
              </a>
            </Button>
            <Button asChild className="w-full bg-green-600 hover:bg-green-700 text-left justify-start">
              <a href="/teams">
                <Users className="w-4 h-4 mr-3" />
                Manage Teams
              </a>
            </Button>
            <Button asChild className="w-full bg-amber-600 hover:bg-amber-700 text-left justify-start">
              <a href="/mentors">
                <UserCheck className="w-4 h-4 mr-3" />
                Connect with Mentors
              </a>
            </Button>
            <Button variant="outline" className="w-full text-left justify-start">
              <BarChart3 className="w-4 h-4 mr-3" />
              View Analytics
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Milestone Roadmap */}
      <Card className="mb-8">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Milestone Roadmap</CardTitle>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm">
              <BarChart3 className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {milestones.map((milestone) => (
              <div
                key={milestone.id}
                className={`border rounded-lg p-4 ${
                  milestone.status === "completed"
                    ? "border-green-300 bg-green-50"
                    : milestone.status === "in_progress"
                    ? "border-blue-300 bg-blue-50"
                    : "border-neutral-300 bg-neutral-50"
                } ${milestone.status === "locked" ? "opacity-60" : ""}`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className={`w-8 h-8 ${getStatusColor(milestone.status)} text-white rounded-full flex items-center justify-center`}>
                    {getStatusIcon(milestone.status)}
                  </div>
                  {getStatusBadge(milestone.status)}
                </div>
                <h3 className="font-medium text-neutral-900 mb-1">{milestone.title}</h3>
                <p className="text-sm text-neutral-600 mb-3">{milestone.description}</p>
                <div className="text-xs text-neutral-500">
                  {milestone.status === "completed" && `Completed ${milestone.completedDate}`}
                  {milestone.status === "in_progress" && `Due in ${milestone.dueDate}`}
                  {milestone.status === "locked" && "Unlocks after milestone 2"}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Team & Mentorship Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Team Members */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Team Members</CardTitle>
            <Button variant="ghost" size="sm">
              <UserPlus className="w-4 h-4" />
            </Button>
          </CardHeader>
          <CardContent className="space-y-4">
            {teamMembers.map((member) => (
              <div key={member.id} className="flex items-center space-x-3">
                <Avatar className="w-10 h-10">
                  <AvatarImage src={member.avatar} alt={member.name} />
                  <AvatarFallback>{member.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <p className="font-medium text-neutral-900">{member.name}</p>
                  <p className="text-sm text-neutral-600">{member.role}</p>
                </div>
                <div 
                  className={`w-3 h-3 rounded-full ${
                    member.status === "online" 
                      ? "bg-green-500" 
                      : member.status === "away" 
                      ? "bg-amber-500" 
                      : "bg-gray-400"
                  }`}
                />
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Mentorship & Support */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Mentorship & Support</CardTitle>
            <Button variant="ghost" size="sm">
              <MessageSquare className="w-4 h-4" />
            </Button>
          </CardHeader>
          <CardContent>
            <div className="border border-blue-200 rounded-lg p-4 bg-blue-50 mb-4">
              <div className="flex items-center space-x-3 mb-3">
                <Avatar className="w-12 h-12">
                  <AvatarImage src={mentor.avatar} alt={mentor.name} />
                  <AvatarFallback>{mentor.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium text-neutral-900">{mentor.name}</p>
                  <p className="text-sm text-neutral-600">{mentor.title}</p>
                </div>
              </div>
              <p className="text-sm text-neutral-600 mb-3">{mentor.experience}</p>
              <Button className="bg-blue-600 hover:bg-blue-700">
                Schedule Session
              </Button>
            </div>

            <div className="space-y-3">
              <h3 className="font-medium text-neutral-900">Recent Feedback</h3>
              <div className="bg-neutral-50 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <MessageSquare className="w-4 h-4 text-blue-600 mt-1 flex-shrink-0" />
                  <div>
                    <p className="text-sm text-neutral-700">"{mentor.recentFeedback}"</p>
                    <p className="text-xs text-neutral-500 mt-1">{mentor.feedbackTime}</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Upload Modal */}
      <MilestoneUploadModal
        open={uploadModalOpen}
        onOpenChange={setUploadModalOpen}
        milestone={selectedMilestone}
      />
    </div>
  );
}
