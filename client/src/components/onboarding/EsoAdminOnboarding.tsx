import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import GooglePlacesAutocomplete from "@/components/ui/google-places-autocomplete";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Building2, Upload, FileText } from "lucide-react";

interface EsoAdminOnboardingProps {
  onComplete: () => void;
}

export default function EsoAdminOnboarding({ onComplete }: EsoAdminOnboardingProps) {
  const [formData, setFormData] = useState({
    organizationName: "",
    location: "",
    organizationSize: "",
    communityGoals: "",
    keyInitiatives: "",
    businessIdeas: "",
    programsLinks: "",
  });
  const [strategicDocument, setStrategicDocument] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const createEsoProfileMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest("POST", "/api/eso-profile", data);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Profile Created!",
        description: "Your ESO organization profile has been set up successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/auth/user"] });
      queryClient.invalidateQueries({ queryKey: ["/api/eso-profile"] });
      onComplete();
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to create profile. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleFileUpload = async (file: File) => {
    if (!file) return null;
    
    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append("file", file);
      
      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      });
      
      if (!response.ok) throw new Error("Upload failed");
      
      const result = await response.json();
      return result.url;
    } catch (error) {
      toast({
        title: "Upload Error",
        description: "Failed to upload document. Please try again.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    let strategicDocumentUrl = null;
    if (strategicDocument) {
      strategicDocumentUrl = await handleFileUpload(strategicDocument);
      if (!strategicDocumentUrl) return; // Upload failed
    }

    createEsoProfileMutation.mutate({
      ...formData,
      strategicDocumentUrl,
    });
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const isFormValid = () => {
    return (
      formData.organizationName &&
      formData.location &&
      formData.organizationSize &&
      formData.communityGoals &&
      formData.keyInitiatives
    );
  };

  return (
    <div className="min-h-screen bg-neutral-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <Card>
          <CardHeader className="text-center">
            <div className="bg-primary text-white p-4 rounded-lg mb-4 inline-block">
              <Building2 className="w-8 h-8" />
            </div>
            <CardTitle className="text-2xl">ESO Organization Setup</CardTitle>
            <p className="text-neutral-600">
              Complete your organization profile to get started with Traction
            </p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Organization Name */}
              <div>
                <Label htmlFor="org-name">Organization Name *</Label>
                <Input
                  id="org-name"
                  value={formData.organizationName}
                  onChange={(e) => handleInputChange("organizationName", e.target.value)}
                  placeholder="Enter your organization name"
                  required
                />
              </div>

              {/* Location */}
              <div>
                <Label htmlFor="location">Location *</Label>
                <GooglePlacesAutocomplete
                  value={formData.location}
                  onChange={(value) => handleInputChange("location", value)}
                  placeholder="Enter your city, state/province, country"
                  required
                />
              </div>

              {/* Organization Size */}
              <div>
                <Label htmlFor="org-size">Organization Size *</Label>
                <Select value={formData.organizationSize} onValueChange={(value) => handleInputChange("organizationSize", value)} required>
                  <SelectTrigger>
                    <SelectValue placeholder="Select organization size" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1-10">1-10 employees</SelectItem>
                    <SelectItem value="10-50">10-50 employees</SelectItem>
                    <SelectItem value="50-100">50-100 employees</SelectItem>
                    <SelectItem value="100+">100+ employees</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Community Goals */}
              <div>
                <Label htmlFor="community-goals">Key Community Issues & Goals *</Label>
                <Textarea
                  id="community-goals"
                  value={formData.communityGoals}
                  onChange={(e) => handleInputChange("communityGoals", e.target.value)}
                  placeholder="Describe the key community issues your organization addresses and your primary goals..."
                  rows={3}
                  required
                />
              </div>

              {/* Key Initiatives */}
              <div>
                <Label htmlFor="key-initiatives">Key Initiatives *</Label>
                <Textarea
                  id="key-initiatives"
                  value={formData.keyInitiatives}
                  onChange={(e) => handleInputChange("keyInitiatives", e.target.value)}
                  placeholder="Describe your key initiatives (e.g., SMB support, High-growth programs, etc.)..."
                  rows={3}
                  required
                />
              </div>

              {/* Strategic Documents Upload */}
              <div>
                <Label htmlFor="strategic-docs">Strategic Documents</Label>
                <div className="border-2 border-dashed border-neutral-300 rounded-lg p-6 text-center">
                  <FileText className="w-8 h-8 text-neutral-400 mx-auto mb-2" />
                  <p className="text-sm text-neutral-600 mb-2">
                    Upload strategic documents, economic development plans (PDF, max 50MB)
                  </p>
                  <input
                    type="file"
                    accept=".pdf"
                    onChange={(e) => setStrategicDocument(e.target.files?.[0] || null)}
                    className="hidden"
                    id="strategic-docs"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => document.getElementById("strategic-docs")?.click()}
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    Choose File
                  </Button>
                  {strategicDocument && (
                    <p className="text-sm text-green-600 mt-2">
                      Selected: {strategicDocument.name}
                    </p>
                  )}
                </div>
              </div>

              {/* Business Ideas */}
              <div>
                <Label htmlFor="business-ideas">Business Ideas for Community</Label>
                <Textarea
                  id="business-ideas"
                  value={formData.businessIdeas}
                  onChange={(e) => handleInputChange("businessIdeas", e.target.value)}
                  placeholder="Do you have any business ideas for your community members to pursue? Describe them here..."
                  rows={4}
                />
              </div>

              {/* Programs Links */}
              <div>
                <Label htmlFor="programs-links">Relevant Programs</Label>
                <Textarea
                  id="programs-links"
                  value={formData.programsLinks}
                  onChange={(e) => handleInputChange("programsLinks", e.target.value)}
                  placeholder="Add program names or paste the URL links to programs that can be featured on the platform..."
                  rows={3}
                />
              </div>

              <Button
                type="submit"
                disabled={!isFormValid() || createEsoProfileMutation.isPending || isUploading}
                className="w-full"
              >
                {createEsoProfileMutation.isPending || isUploading
                  ? "Setting up profile..."
                  : "Complete Setup"
                }
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
