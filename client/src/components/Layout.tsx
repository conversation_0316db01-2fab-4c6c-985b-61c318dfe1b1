import { useState } from "react";
import { Link, useLocation } from "wouter";
import { useAuth } from "@/hooks/useAuth";
import { useProfileCompletion } from "@/hooks/useProfileCompletion";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Rocket, Menu, Users, Lightbulb, UserCheck, TrendingUp } from "lucide-react";

interface LayoutProps {
  children: React.ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  const { user, isAuthenticated } = useAuth();
  const { profileCompleted } = useProfileCompletion();
  const [location] = useLocation();
  const [activeRole, setActiveRole] = useState(user?.role || "entrepreneur");

  // ProtectedRoute handles authentication and profile completion redirects
  // Layout should only render for authenticated users with completed profiles
  if (!isAuthenticated || !user || !profileCompleted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-neutral-50">
        <div className="text-center">
          <div className="bg-primary text-white p-4 rounded-lg mb-4 inline-block">
            <Rocket className="w-8 h-8 animate-pulse" />
          </div>
          <p className="text-neutral-600">Loading...</p>
        </div>
      </div>
    );
  }

  const navigation = [
    { name: "Dashboard", href: "/", icon: TrendingUp },
    { name: "Ideas", href: "/ideas", icon: Lightbulb },
    { name: "Teams", href: "/teams", icon: Users },
    { name: "Mentors", href: "/mentors", icon: UserCheck },
  ];

  return (
    <div className="min-h-screen bg-neutral-50">
      {/* Navigation Header */}
      <header className="bg-white border-b border-neutral-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link href="/" className="flex items-center">
              <div className="bg-primary text-white p-2 rounded-lg mr-3">
                <Rocket className="w-5 h-5" />
              </div>
              <span className="text-xl font-bold text-neutral-900">Traction</span>
              <span className="text-sm text-neutral-500 ml-2">by Skills Gap Advocate Inc.</span>
            </Link>

            {/* Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center space-x-1 text-sm transition-colors ${
                    location === item.href
                      ? "text-primary"
                      : "text-neutral-600 hover:text-primary"
                  }`}
                >
                  <item.icon className="w-4 h-4" />
                  <span>{item.name}</span>
                </Link>
              ))}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center space-x-2">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={user.profileImageUrl || ""} alt={user.firstName || ""} />
                      <AvatarFallback>
                        {user.firstName?.[0]}{user.lastName?.[0]}
                      </AvatarFallback>
                    </Avatar>
                    <span>{user.firstName} {user.lastName}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>Profile Settings</DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <a href="/api/logout">Sign Out</a>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </nav>

            {/* Mobile menu button */}
            <Button variant="ghost" size="sm" className="md:hidden">
              <Menu className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </header>

      <div className="flex min-h-screen">
        {/* Sidebar */}
        <aside className="hidden lg:flex lg:flex-col w-64 bg-white border-r border-neutral-200">
          <div className="flex-1 p-6">
            {/* Role Switcher */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-neutral-700 mb-2">Active Role</label>
              <Select value={activeRole} onValueChange={setActiveRole}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="entrepreneur">Entrepreneur</SelectItem>
                  <SelectItem value="eso_admin">ESO Admin</SelectItem>
                  <SelectItem value="mentor">Mentor</SelectItem>
                  <SelectItem value="sponsor">Sponsor</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Quick Stats */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-neutral-700 mb-3">Quick Overview</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-neutral-600">Active Projects</span>
                  <span className="bg-primary text-white text-xs px-2 py-1 rounded-full">0</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-neutral-600">Completed Milestones</span>
                  <span className="bg-secondary text-white text-xs px-2 py-1 rounded-full">0</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-neutral-600">Team Members</span>
                  <span className="bg-accent text-white text-xs px-2 py-1 rounded-full">0</span>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div>
              <h3 className="text-sm font-semibold text-neutral-700 mb-3">Recent Activity</h3>
              <div className="text-center py-8">
                <p className="text-sm text-neutral-500">No recent activity</p>
                <p className="text-xs text-neutral-400 mt-1">Start your first project to see updates here</p>
              </div>
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
