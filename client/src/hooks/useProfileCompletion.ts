import { useQuery } from "@tanstack/react-query";
import { useAuth } from "./useAuth";
import { isProfileComplete, getOnboardingRoute } from "@/lib/profileUtils";
import type { User, Eso } from "@shared/schema";

// Type guard to check if user is a valid User object
function isValidUser(user: any): user is User {
  return user && typeof user === 'object' && 'role' in user && 'id' in user;
}

export function useProfileCompletion() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();

  // For ESO admins, we need to check if they have an ESO profile
  const { data: esoProfile, isLoading: esoLoading, error: esoError } = useQuery<Eso | null>({
    queryKey: ["/api/eso-profile"],
    queryFn: async () => {
      const response = await fetch("/api/eso-profile", {
        credentials: "include",
      });

      if (response.status === 401) {
        return null; // Return null for unauthorized instead of throwing
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    },
    enabled: isAuthenticated && isValidUser(user) && user.role === "eso_admin",
    retry: false,
  });


  // Calculate if we're still loading any required data
  // If there's an error in ESO query, don't consider it as loading
  const isLoading = authLoading || (isValidUser(user) && user.role === "eso_admin" && esoLoading && !esoError);

  const profileCompleted = (() => {
    if (!isValidUser(user)) return false;

    if (user.role === "eso_admin") {
      // ESO admin needs both user profile and ESO organization profile
      // If still loading ESO profile, consider it incomplete for now
      if (esoLoading && !esoError) {
        return false;
      }

      // If there's an error or no ESO profile exists yet, profile is incomplete (needs onboarding)
      if (esoError || !esoProfile) {
        return false;
      }

      // Check if ESO profile is marked as completed
      return !!(esoProfile.profileCompleted);
    }

    return isProfileComplete(user);
  })();



  const onboardingRoute = isValidUser(user) ? getOnboardingRoute(user) : "/onboarding/entrepreneur";

  return {
    profileCompleted,
    onboardingRoute,
    user,
    esoProfile,
    isLoading,
  };
}
